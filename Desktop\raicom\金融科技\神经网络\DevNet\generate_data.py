import torch
import numpy as np
import os
import argparse
from sklearn.model_selection import train_test_split
from utils import dataLoading, get_data_from_svmlight_file
from fraud_generator import FraudDataGenerator
from model import get_device

np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed(42)

def generate_training_data(args):
    names = args.data_set.split(',')
    device = get_device()
    
    for nm in names:
        filename = nm.strip()
        data_format = int(args.data_format)
        
        print(f"处理数据集: {filename}")
        
        train_data_dir = os.path.join('./dataset', filename, 'train')
        test_data_dir = os.path.join('./dataset', filename, 'test')
        os.makedirs(train_data_dir, exist_ok=True)
        os.makedirs(test_data_dir, exist_ok=True)
        
        if data_format == 0:
            x, labels = dataLoading(args.input_path + filename + ".csv")
        else:
            x, labels = get_data_from_svmlight_file(args.input_path + filename + ".svm")
            x = x.tocsr()
        
        fraud_indices = np.where(labels == 1)[0]
        normal_indices = np.where(labels == 0)[0]
        
        fraud_data = x[fraud_indices]
        normal_data = x[normal_indices]
        
        print(f"原始数据: 诈骗样本 {len(fraud_indices)}, 正常样本 {len(normal_indices)}")
        
        for run in range(args.runs):
            print(f"生成第 {run+1} 轮数据")
            
            normal_train, normal_test, _, _ = train_test_split(
                normal_data, np.zeros(len(normal_data)), 
                test_size=0.2, random_state=42
            )
            
            x_test = np.vstack([fraud_data, normal_test])
            y_test = np.hstack([np.ones(len(fraud_data)), np.zeros(len(normal_test))])
            
            test_filename = f"test_data_run{run+1}.npz"
            test_path = os.path.join(test_data_dir, test_filename)
            
            if data_format == 1:
                x_test_save = x_test.toarray()
            else:
                x_test_save = x_test
                
            np.savez(test_path, 
                    x_test=x_test_save, 
                    y_test=y_test,
                    fraud_count=len(fraud_data),
                    normal_count=len(normal_test))
            print(f"测试数据已保存到: {test_path}")
            
            # 准备训练数据生成
            if data_format == 1:
                fraud_data_dense = fraud_data.toarray()
                normal_train_dense = normal_train.toarray()
            else:
                fraud_data_dense = fraud_data
                normal_train_dense = normal_train
            
            # 训练VAE生成器
            print("训练诈骗数据生成器...")
            generator = FraudDataGenerator(x.shape[1], device)
            generator.fit(fraud_data_dense, epochs=300, batch_size=32)
            
            # 计算需要生成的诈骗样本数量（1:9比例）
            n_normal_train = len(normal_train)
            n_generated_fraud = n_normal_train // 9  # 1:9比例
            
            print(f"生成 {n_generated_fraud} 个合成诈骗样本用于训练")
            generated_fraud = generator.generate_fraud_samples(n_generated_fraud)
            
            # 构建训练集：生成的诈骗数据 + 正常数据
            x_train = np.vstack([generated_fraud, normal_train_dense])
            y_train = np.hstack([np.ones(n_generated_fraud), np.zeros(len(normal_train))])
            
            print(f"训练集: 生成诈骗样本 {n_generated_fraud}, 正常样本 {len(normal_train)}")
            
            # 保存训练数据
            train_filename = f"train_data_run{run+1}.npz"
            train_path = os.path.join(train_data_dir, train_filename)
            
            np.savez(train_path,
                    x_train=x_train,
                    y_train=y_train,
                    generated_fraud_count=n_generated_fraud,
                    normal_train_count=len(normal_train),
                    fraud_indices=np.arange(n_generated_fraud),
                    normal_indices=np.arange(n_generated_fraud, len(x_train)))
            print(f"训练数据已保存到: {train_path}")
            
            print(f"第 {run+1} 轮数据生成完成")
            print("=" * 50)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--runs", type=int, default=1, help="生成数据的轮数")
    parser.add_argument("--input_path", type=str, default='./dataset/', help="原始数据集路径")
    parser.add_argument("--data_set", type=str, default='creditcardfraud_normalised_2013', help="数据集名称")
    parser.add_argument("--data_format", choices=['0','1'], default='0', help="数据格式")
    args = parser.parse_args()
    generate_training_data(args)