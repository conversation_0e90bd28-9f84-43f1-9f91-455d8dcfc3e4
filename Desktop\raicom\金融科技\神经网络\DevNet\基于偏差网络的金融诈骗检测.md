# ***\*2\**** ***\*实验数据\****

本研究选用两个具有代表性的金融诈骗数据集开展模型训练与测试工作。这两个数据集分属不同类型，但在数据结构与特征维度上存在较高相似度，其核心差异集中体现在欺诈样本的分布比例上，可分别用于模拟金融交易中“样本极度不平衡”与“样本相对均衡”的典型场景。以下是对两个数据集的详细阐述：

## 2.1 Credit Card Fraud Detection

Credit Card Fraud Detection源自Kaggle平台(https://www.kaggle.com/datasets/mlg-ulb/creditcardfraud)，收录了2013年9月欧洲信用卡用户的交易记录。数据时间跨度为两天，总计包含284,807条交易记录，其中经标注的欺诈交易仅有492笔，正类样本占比仅为0.172%，呈现出显著的类别不均衡特征。数据集包含经过PCA降维处理的数值型变量，具体特征说明如下表所示：

**表** **1** **Credit Card Fraud Detection数据描述**

| ***\*特征名称\**** | ***\*特征描述\****                                |
| ------------------ | ------------------------------------------------- |
| V1-V28             | 通过PCA提取的主成分特征，已完成维度压缩与信息重构 |
| 时间               | 每笔交易与数据集首笔交易的时间间隔(单位: 秒)      |
| 金额               | 原始交易金额，可作为成本敏感学习的参考            |
| 类别               | 标签变量，以 0 表示正常交易，1 表示欺诈交易       |

 

## 2.2 Credit Card Fraud Detection Dataset 2023

作为前者的升级版本，Credit Card Fraud Detection Dataset 2023(https://www.kaggle.com/datasets/nelgiriyewithana/credit-card-fraud-detection-dataset-2023)采集了2023年欧洲信用卡用户的交易数据，共包含568,630条记录。与前一数据集相比，其最大特点是实现了正负样本的均衡分布(正常交易与欺诈交易比例为1:1)，这种数据分布更有利于模型在均衡场景下的性能验证。数据的各特征说明如下表所示：

**表** **2** **Credit Card Fraud Detection Dataset 2023数据描述**

| ***\*特征名称\**** | ***\*特征描述\****                                   |
| ------------------ | ---------------------------------------------------- |
| id                 | 每笔交易的唯一标识符                                 |
| V1-V28             | 匿名化的交易属性特征，涵盖时间、地点、交易模式等维度 |
| 金额               | 保留的交易金额信息                                   |
| 类别               | 标签变量，以0表示正常交易，1表示欺诈交易             |

# ***\*3\**** ***\*所采用的深度学习方法的原理\****

## 3.1 DevNet

偏差网络(Deviation Network, 简称DevNet)是一种面向异常检测任务的深度学习模型，其核心思想是通过端到端的方式直接学习每个样本的异常得分。相较于传统的两阶段方法(先学习特征表示，再进行异常评分)，DevNet直接优化异常评分函数，使其在类别极度不平衡的数据上具有更强的表达能力和泛化能力。

### 3.1.1 网络结构

本研究中使用的DevNet模型采用前馈神经网络结构，由三个隐藏层构成，分别包含1000、250和20个神经元，末尾连接一个输出层，用于生成每个样本的异常得分。其网络结构如下所示：

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml14620\wps52.jpg) 

**图** **1** **前馈神经网络网络结构**

在实现中，网络使用ReLU激活函数进行非线性建模。整体网络结构定义如下：

```
class DevNetD(nn.Module):
    def __init__(self, input_dim):
        super(DevNetD, self).__init__()
        self.layer1 = nn.Linear(input_dim, 1000)
        self.layer2 = nn.Linear(1000, 250)
        self.layer3 = nn.Linear(250, 20)
        self.layer4 = nn.Linear(20, 1)
        self.weight_decay = 0.01

    def forward(self, x):
        x = F.relu(self.layer1(x))
        x = F.relu(self.layer2(x))
        x = F.relu(self.layer3(x))
        return self.layer4(x)
```

将上述网络形式化表示为一个函数φ(x; θ)，其中x是输入样本，θ表示网络参数，输出为 标量异常得分：„

φ(x; θ) = η(ψ(x; θt); θs)

其中，ψ(·)为中间表示学习函数，η(·)为线性输出层函数。

### 3.1.2 损失函数

交叉熵损失函数在面对类别分布严重不均衡的数据时，容易导致模型在训练过程中偏向多数类，过度拟合正常样本，进而削弱对少数类的识别能力，甚至出现识别失效的情况。与传统基于交叉熵的二分类损失不同，DevNet采用了一种基于Z分数的偏差损失函数，以增强模型对异常样本的敏感度。该损失函数的设计思想是：令正常样本的异常得分围绕参考分数聚集，而异常样本则应显著偏离该分布的右尾。具体损失函数定义如下：

dev(x) = Φ(x; θ) − μR

L(Φ(x; θ), μR, σR) = (1 − y) · [dev(x)] + y · max(0, α − dev(x))

在(2)式中，dev(x)代表偏差函数，用于将所有的样本围绕参考分数聚集。在(3)式中，y = 1 表示为异常样本，y = 0 表示正常样本，μR和σR分别为正常样本异常得分的均值和标准差， 来自高斯先验，α为Z分数的显著性阈值，本文设置为5。

在损失函数中，对于正常的样本，模型通过最小化其异常得分的偏差，强制其异常得分 接近均值为μr，方差为σR的参考分布。对于异常样本，模型通过最大化其异常得分的偏差，强 制其远离参考分布的右尾，显著提高模型在极度不平衡数据场景中的识别能力。

### 3.1.3 优化算法

在模型训练过程中，DevNet采用RMSprop优化器对参数进行更新，并结合小批量梯度下降(Mini-Batch SGD)策略进行迭代训练。考虑到异常样本在金融数据中占比极低，为保证训练效果，模型对训练集进行控制采样，仅保留固定数量的标注异常样本，同时引入由异常样本生成的伪正常样本，扩展数据规模并增加样本多样性，缓解样本不平衡问题。

在损失函数的基础上，模型还引入了L2正则化项，旨在抑制参数过大带来的过拟合风险。L2正则化通过对模型中所有权重参数引入惩罚项，使其保持在较小范围，从而提升模型在未知数据上的泛化能力。具体设置中，模型的weight_decay参数设为0.01，并直接传入优化器中生效。

整个训练流程包括数据处理、构建训练集、模型定义、损失函数与优化器配置以及模型训练五个步骤，整体流程的伪代码如下：

```
def train_devnet(data, labels):
    x_train, x_test, y_train, y_test = train_test_split(data, labels)
    保留部分异常样本，注入伪正常样本
    # 构建DataLoader
    dataset = DevNetDataset(x_train, 异常索引, 正常索引)
    loader = DataLoader(dataset, batch_size=128, shuffle=True)
    model = DevNet(input_dim)
    optimizer = RMSprop(model.parameters(), lr=0.001, weight_decay=0.01)
    loss_fn = DeviationLoss()
    for epoch in range(EPOCHS):
        for data_batch, label_batch in loader:
            optimizer.zero_grad()
            output = model(data_batch)
            loss = loss_fn(output, label_batch)
            loss.backward()
            optimizer.step()

    return model
```

## 3.2 VAE(Variational AutoEncoder)

在金融欺诈检测任务中，数据类别通常存在极度不平衡的情况，异常(诈骗)样本远少于正常样本。缓解这一问题的常见思路主要有两类：第一类方法是通过提高异常样本的权重，使模型在训练过程中更关注少数类，从而提升对诈骗行为的识别能力，DevNet正是此类方法的代表；第二类方法则通过调整数据分布来平衡样本比例，常见的手段包括对异常样本进行过采样(如SMOTE)或对正常样本进行欠采样[5]。这些方法虽在一定程度上缓解了类别不平衡，但也存在明显缺陷：过采样可能导致模型过拟合少量重复的异常样本，降低泛化能力；而欠采样则可能丢失大量有价值的正常样本信息，影响模型的整体表现。

为克服上述方法的局限性，本研究引入了变分自编码器(Variational AutoEncoder, VAE)[6]作为生成模型。VAE通过学习原始诈骗样本在潜在空间中的分布特征，能够生成与真实诈骗交易高度相似的合成样本，从而在不引入冗余或信息损失的情况下，扩充异常样本数量，缓解数据类别失衡对模型训练的负面影响。该策略不仅提高了模型对诈骗样本的识别能力，也有效保持了训练数据的多样性与完整性。

### 3.2.1 网络结构

VAE模型整体由编码器(Encoder)、重参数化模块(Reparameterization)和解码器(Decoder)三部分组成，其网络结构如图所示：

![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml14620\wps53.jpg) 

**图** **2** **Variational AutoEncoder网络结构图**

编码器将输入样本映射为潜在变量的均值和对数方差，通过重参数化技巧采样出潜在变量，再由解码器将其还原为重构样本。该过程可形式化表示为：

编码器：„

μ,logσ² = f_enc(x)„

重参数化技巧：„

z = μ + σ⊙ε; ε ~ N(0,1)„

解码器：„

ˆx = f_dec(z)„

VAE的实现主代码如下所示:

```
class FraudVAE(nn.Module):
    def __init__(self, input_dim, latent_dim=20, hidden_dim=128):
        super(FraudVAE, self).__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.ReLU()
        )
        self.fc_mu = nn.Linear(hidden_dim//2, latent_dim)
        self.fc_logvar = nn.Linear(hidden_dim//2, latent_dim)

        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, hidden_dim//2),
            nn.ReLU(),
            nn.Linear(hidden_dim//2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, input_dim)
        )

    def encode(self, x):
        h = self.encoder(x)
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        return mu, logvar

    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std

    def decode(self, z):
        return self.decoder(z)

    def forward(self, x):
        mu, logvar = self.encode(x)
        z = self.reparameterize(mu, logvar)
        return self.decode(z), mu, logvar
```

### 3.2.2 损失函数

VAE的损失函数由两部分组成：重构误差与KL散度。前者衡量输入样本与重构样本之间的距离，后者约束潜在空间的分布接近标准正态分布。整体损失函数定义如下：

L_{VAE}(x, \hat{x}, \mu, \log\sigma^2) = \underbrace{\|x - \hat{x}\|^2}_{\text{重构损失}} + \beta \cdot \underbrace{D_{KL}(q(z|x) \| p(z))}_{\text{KL散度}}

其中，KL散度为：

D_{KL} = -\frac{1}{20} \sum_{i=1}^{a} (1 + \log\sigma_i^2 - \mu_i^2 - \sigma_i^2)

在实现中，重构误差使用均方误差(MSE)，完整损失函数代码如下：

```
def vae_loss(recon_x, x, mu, logvar, beta=1.0):
    recon_loss = F.mse_loss(recon_x, x, reduction='sum')
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
return recon_loss + beta * kl_loss
```

### 3.2.3 优化方法

VAE的训练采用Adam优化器进行参数更新，目标为最小化上述损失函数。整体训练过程包括数据标准化、构建TensorDataset、批训练与优化器迭代，伪代码如下：

```
def fit(self, fraud_data, epochs=100, batch_size=32, lr=1e-3):
    fraud_data_scaled = self.scaler.fit_transform(fraud_data)
    dataset = TensorDataset(torch.FloatTensor(fraud_data_scaled))
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    optimizer = torch.optim.Adam(self.vae.parameters(), lr=lr)
    self.vae.train()
    for epoch in range(epochs):
        total_loss = 0
        for (data,) in dataloader:
            data = data.to(self.device)
            optimizer.zero_grad()
            recon_data, mu, logvar = self.vae(data)
            loss = vae_loss(recon_data, data, mu, logvar)
            loss.backward()
            optimizer.step()
            total_loss += loss.item()
```

## 3.3 基于弱分类器的投票集成学习

通过在前两节中引入偏差网络(DevNet)与生成模型(VAE)，本研究显著增强了模型对金融诈骗样本的识别能力。尤其是借助VAE合成的高质量异常样本，有效缓解了原始数据中的类别失衡问题，使得DevNet能够充分学习诈骗交易的分布特征。然而，在实际实验过程中也发现，由于DevNet模型在训练时刻意提升了对异常样本的关注，其对正常样本的识别能力出现下降现象。在某些测试场景中，模型对诈骗交易的检测准确率虽高达98%以上，但对正常交易的识别准确率却降低至接近50%，造成整体性能失衡。

为了进一步提升模型在正常样本与异常样本之间的判别能力，并提高整体检测的鲁棒性与泛化能力，本文设计了一种基于弱分类器的投票集成学习策略。该方法的核心思想是在训练阶段构建多个具有差异性的弱模型，每个模型仅在数据的一个子集上训练，并利用投票机制融合各模型的判断结果，从而弥补单一模型偏向异常样本的风险，提升对正常样本的准确检测能力。

具体而言，首先将完整的训练集划分为5个子集，每个子集均包含一定比例的正常样本与合成诈骗样本。随后，分别以这5个子集训练5个独立的DevNet模型，每个模型都可视作一个弱分类器。训练完成后，对于每一个测试样本，5个模型将分别给出其是否为诈骗的预测结果。由于各模型对异常交易的判别能力普遍较强，为确保预测的高置信度，本文采用了严格多数投票机制：仅当5个模型全部预测为诈骗交易时，该测试样本才最终被判定为诈骗交易；否则，将其归为正常交易。这个集成策略不仅提高了预测的稳定性，也极大降低了因某一模型误判带来的整体精度下降风险。投票机制可形式化表达为：

\hat{y} = 
\begin{cases} 
0, r \cdot \sum_{i=1}^5 h_i(x) = 5 \\
1, \text{否则}
\end{cases}

其中，h_i(x)表示第i部分类器对样本的预测结果(1表示诈骗，0表示正常)，为最终的 集成判定结果。„

# ***\*4\**** ***\*实验设置、实验结果及分析\****

## 4.1 实验设置

### 4.1.1 评估指标

为全面评估模型在金融诈骗检测任务中的性能表现，本文采用准确率(Accuracy)、精度(Precision)、F1分数(F1 Score)、AUC-ROC(Area Under the Receiver Operating Characteristic Curve)、AUC-PR(Area Under the Precision-Recall Curve)五种主流分类评估指标。由于实验数据具有高度类别不平衡特征，因此每项指标均从整体与少数类识别的角度出发进行评估。

***\*(1)\**** ***\*准确率\*******\*(\*******\*Accuracy\*******\*)\****

准确率衡量模型预测正确的样本占全部样本的比例，是最基础的评估指标。其公式为：

Accuracy = \frac{TP + TN}{TP + TN + FP + FN}^u

其中，TP：真正例，即将诈骗交易正确识别为诈骗；TN：真负例，即将正常交易正确识别为正常；FP：假正例，即将正常交易误判为诈骗；FN：假负例，即将诈骗交易误判为正常。

***\*(2)\**** ***\*精度(\*******\*Precision\*******\*)\****

精度用于衡量预测为正类(诈骗)的样本中，真正为正类的比例，反映了模型在检测诈骗交易时的准确性：

Precision = \frac{TP}{TP + FP}^u

高精度意味着模型在预测为诈骗时具有较强的置信度，能够减少对正常交易的误报。

***\*(3)\**** ***\*F1分数\*******\*(\*******\*F1 Score\*******\*)\****

F1分数是精度(Precision)与召回率(Recall)的调和平均数，综合考虑了模型的误报率与漏报率：

F1\text{-}Score = \frac{2 \cdot Precision \cdot Recall}{Precision + Recall}^u

其中召回率(Recall)定义如下：

Recall = \frac{TP}{TP + FN}^*

***\*(4)\**** ***\*AUC-ROC\*******\*(\*******\*Area Under the Receiver Operating Characteristic Curve\*******\*)\****

AUC-ROC衡量模型区分正负类样本能力的强弱，反映了在不同阈值下模型的整体表现。ROC曲线以假正率(FPR)为横轴、真正率(TPR)为纵轴绘制，AUC值定义为曲线下的面积：

AUC - ROC = \int_0^1 TPR(t)dFPR(t)

其中: TPR = \frac{TP}{TP+FN} 表示真正率; FPR = \frac{TP}{FP+TN} 表示假正率。AUC - ROC值越接近 1，模型整体识别能力越强。

***\*(5)\**** ***\*AUC-PR\*******\*(\*******\*Area Under the Precision-Recall Curve\*******\*)\****

AUC-PR是在数据高度不平衡时更具代表性的指标，通过Precision-Recall曲线度量模型在不同阈值下的表现：

AUC - PR = \int_0^1 Precision(t)dRecall(t)

与ROC曲线不同，PR曲线更关注正类(诈骗交易)的检测效果。AUC-PR值越高，表示模型在少数类的识别上表现越好，尤其适用于本研究这种诈骗样本比例极低的场景。

### 4.1.2 实验参数设置

在实验过程中，本研究设置的参数如表3所受。

**表** **3** **实验参数设置表**

| ***\*参数名称\**** | ***\*参数值\**** | ***\*参数说明\****                 |
| ------------------ | ---------------- | ---------------------------------- |
| network_depth      | 4                | 网络深度，对应网络的层数           |
| batch_size         | 512              | 每个批次处理的数据量               |
| nb_batch           | 20               | 每轮epoch的最大批次数              |
| epochs             | 50               | 训练轮数                           |
| optimizer          | RMSprop          | 模型优化器                         |
| learning_rate      | 0.001            | 学习率                             |
| weight_decay       | 0.01             | L2正则化权重衰减                   |
| loss_function      | DeviationLoss    | 偏差损失函数，用于处理异常检测任务 |
| vote_threshold     | 5                | 集成模型中预测为诈骗的最小票数     |

在具体实验中，模型的网络深度设置为4，即由4层前馈神经网络构成，隐藏层数为3，该深度在保证模型表达能力的前提下，能够较好地学习数据中的非线性特征分布。在批次大小方面，选择了较大的batch_size=512，结合nb_batch=20的设置，使每个epoch的最大迭代次数限定在20次，从而在不增加显存负担的情况下控制训练时间，提高了训练过程的稳定性。训练轮数,(epochs)设置为50，经过多轮实验验证，该值在精度和训练时间之间取得了良好平衡，能够充分训练模型而不易出现过拟合。

在优化器方面，本研究采用RMSprop优化算法，其在处理非平稳目标函数时具有良好的鲁棒性，有利于在类别不平衡条件下更稳定地收敛。学习率设置为0.001，结合weight_decay = 0.01的L2正则化策略，有效抑制过拟合风险，提升了模型的泛化能力。在损失函数上，采用本文核心的DeviationLoss偏差损失函数，通过Z分数机制将正常样本与异常样本分别拉向参考分布的不同区域，从而增强模型对极少数诈骗样本的识别能力。

在模型预测阶段，为确保最终判断的可靠性，集成模型采用了严格的多数投票策略：仅当5个子模型全部预测为诈骗时，最终结果才判定为诈骗交易(即vote_threshold=5)。这一策略虽然在一定程度上牺牲了对异常样本的召回率，但显著提升了整体模型预测结果的精度与稳定性，有效缓解了偏差网络在正常样本检测上的性能下降问题。

## 4.2 实验结果及分析

为了详细展示模型的效果，本文选择了两种不同的数据对不同的模型实验结果进行了详细的演示与分析。

### 4.2.1 Credit Card Fraud Detection数据集实验结果

表4展示了四种模型在整体测试集上的性能对比。CrossEntropyLoss表示采用传统交叉熵损失函数训练的基线模型。DeviationLoss表示使用基于Z分数设计的偏差损失函数，以提升模型对异常样本的敏感性。VAE+DeviationLoss模型在训练阶段利用VAE生成模型对诈骗样本按照1:9的比例进行扩增，增强异常类的判别能力，同时保持正常样本数据量不变，测试阶段仍使用未扩增的真实异常样本。集成模型则基于本文提出的弱分类器投票机制，融合多个子模型的判断结果，以提升整体检测的稳定性和鲁棒性。

**表** **4** **整体性能对比**

| ***\*Model\****             | ***\*A\*******\*ccuracy\*******\*(%)\**** | ***\*P\*******\*recision\*******\*(%)\**** | ***\*F\*******\*1 score(%)\****     | ***\*AUCROC\*******\*(%)\****       | ***\*AUCPR\*******\*(%)\****        |
| --------------------------- | ----------------------------------------- | ------------------------------------------ | ----------------------------------- | ----------------------------------- | ----------------------------------- |
| ***\*CrossEntropyLoss\****  | 50.04                                     | 0.20                                       | 0.40                                | 56.54                               | 17.97                               |
| ***\*DeviationLoss\****     | 50.17                                     | 0.34                                       | 0.68                                | ***\*98\*******\*.\*******\*00\**** | 70.89                               |
| ***\*VAE+DeviationLoss\**** | 50.82                                     | 1.68                                       | 3.30                                | 97.55                               | 85.42                               |
| ***\*集成模型\****          | ***\*95\*******\*.\*******\*78\****       | ***\*15\*******\*.\*******\*82\****        | ***\*26\*******\*.\*******\*94\**** | 97.54                               | ***\*85\*******\*.\*******\*52\**** |

从表4中可以看出，使用传统的CrossEntropyLoss模型在准确率(Accuracy)上仅为50.04%，精度(Precision)为0.20%，F1分数仅为0.40%，AUC-ROC仅为56.54%，说明该模型几乎无法有效区分诈骗与正常交易。这是因为在高度不平衡数据下，交叉熵损失函数倾向于预测多数类，导致其在少数类诈骗检测任务中失效。

相较之下，DeviationLoss 显著提升了AUC-ROC(98.00%)和AUCPR(70.89%)，但在准确率、精度和F1分数方面仍然偏低。这表明尽管该方法在区分能力上有大幅提升，但在具体二分类任务中仍受到样本不均衡的限制，精度(0.34%)和F1分数(0.68%)仍未达到实用水平。

引入VAE生成模型后(VAE+DeviationLoss)，通过对诈骗样本进行1:9扩充，模型的精度提升至1.68%，F1分数也增长至3.30%，说明合成数据增强了少数类的判别能力。AUC-ROC与AUCPR依旧保持在97%以上和85%左右，体现出模型总体性能稳定。

集成模型在所有指标上实现突破，准确率高达95.78%，精度15.82%，F1分数26.94%。虽然AUC略低于单模型(97.54%)，但精度和稳定性显著优于其他方法，说明集成策略有效缓解了偏差网络过度关注异常样本而忽视正常样本的问题，极大提升了模型的实际可用性。

**表** **5** **正常数据表现**

| ***\*Model\****             | ***\*样本数\**** | ***\*A\*******\*ccuracy\*******\*(%)\**** |
| --------------------------- | ---------------- | ----------------------------------------- |
| ***\*CrossEntropyLoss\****  | 56864            | 50.02                                     |
| ***\*DeviationLoss\****     | 56864            | 50.08                                     |
| ***\*VAE+DeviationLoss\**** | 56863            | 50.42                                     |
| ***\*集成模型\****          | 56863            | ***\*95\*******\*.\*******\*83\****       |

表5分析了四种模型在全为正常样本的子集上的检测表现。CrossEntropyLoss、DeviationLoss和VAE+DeviationLoss的准确率分别仅为50.02%、50.08%和50.42%，几乎与随机预测持平。这再次证明这三类模型普遍存在正常样本判别失效问题。而集成模型在正常样本上的识别准确率高达95.83%，说明通过投票机制对正常交易具有更高的容错能力和稳定性，有效提升了对主类的判别效果。这正是集成方法弥补DevNet局限性的核心贡献之一。

**表** **6** **异常数据表现**

| ***\*Model\****             | ***\*样本数\**** | ***\*A\*******\*ccuracy\*******\*(%)\**** |
| --------------------------- | ---------------- | ----------------------------------------- |
| ***\*CrossEntropyLoss\****  | 98               | 58.16                                     |
| ***\*DeviationLoss\****     | 98               | ***\*98\*******\*.\*******\*98\****       |
| ***\*VAE+DeviationLoss\**** | 492              | 97.97                                     |
| ***\*集成模型\****          | 492              | 90.65                                     |

表6展示了各模型在全为异常样本的子集中的表现。DeviationLoss取得了最高的准确率(98.98%)，证明其对少数类的强表征能力。但其过度强调异常样本的学习，导致正常样本被误判，从而拉低了整体准确率。VAE+DeviationLoss在诈骗样本上保持97.97%的高识别率，说明VAE生成的伪异常样本具有良好代表性，提升了模型泛化性能。而集成模型准确率为90.65%，略低于上述两个模型，但考虑到其在正常样本中的大幅提升，这一“牺牲”是可接受的，尤其适用于追求低误报率的金融风控场景。

**表** **7** **弱分类器集成模型效果对比**

| ***\*Model\**** | ***\*A\*******\*ccuracy\*******\*(%)\**** | ***\*P\*******\*recision\*******\*(%)\**** | ***\*F\*******\*1 score(%)\**** | ***\*AUCROC\*******\*(%)\**** | ***\*AUCPR\*******\*(%)\**** |
| --------------- | ----------------------------------------- | ------------------------------------------ | ------------------------------- | ----------------------------- | ---------------------------- |
| 模型1           | 50.83                                     | 1.68                                       | 3.31                            | 97.55                         | 85.42                        |
| 模型2           | 50.82                                     | 1.68                                       | 3.30                            | 97.60                         | 85.48                        |
| 模型3           | 50.74                                     | 1.58                                       | 3.11                            | 92.42                         | 85.06                        |
| 模型4           | 50.82                                     | 1.68                                       | 3.30                            | 97.52                         | 85.44                        |
| 模型5           | 50.82                                     | 1.68                                       | 3.30                            | 97.39                         | 85.63                        |
| 集成模型        | 95.78                                     | 15.82                                      | 26.94                           | 97.54                         | 85.52                        |

表7进一步对比了每个子模型与集成模型的性能。可以看出，单个模型在所有评估指标(Accuracy≈50.8%，Precision≈1.68%，F1≈3.30%，AUC-ROC≈97.5%，AUC-PR≈85.5%)表现高度一致，说明每个弱分类器都有效地学习到了异常分布。但由于正常样本表现较差，导致准确率偏低。集成模型通过票数约束机制，在保持高AUC的前提下，显著提高了精度(15.82%)和F1分数(26.94%)，解决了单一模型检测偏斜问题，验证了集成策略在极度不平衡数据下的有效性。

### 4.2.2 Credit Card Fraud Detection Dataset 2023数据集实验结果

由于Credit Card Fraud Detection Dataset 2023数据集相较于Credit Card Fraud Detection数据集在异常数量上明显扩充，且异常样本与正常样本的比例为1:1，因此该数据集不存在样本分布不均衡的问题。在本次实验中，未采用VAE对异常数据进行扩增，一方面是由于数据本身已经平衡，无需额外采样；另一方面，数据集中包含284,315条诈骗交易记录，样本数量庞大，会显著增加VAE模型训练的难度和时间成本。因此，在该数据集上仅使用基于Z分数的偏差损失函数进行建模，验证其在平衡数据场景下的有效性。

**表** **8** **整体性能对比**

| ***\*Model\****            | ***\*A\*******\*ccuracy\*******\*(%)\**** | ***\*P\*******\*recision\*******\*(%)\**** | ***\*F\*******\*1 score(%)\****     | ***\*AUCROC\*******\*(%)\****       | ***\*AUCPR\*******\*(%)\****        |
| -------------------------- | ----------------------------------------- | ------------------------------------------ | ----------------------------------- | ----------------------------------- | ----------------------------------- |
| ***\*CrossEntropyLoss\**** | 47.25                                     | 47.25                                      | 45.90                               | 50.11                               | 47.24                               |
| ***\*DeviationLoss\****    | ***\*89\*******\*.\*******\*56\****       | ***\*89\*******\*.\*******\*56\****        | ***\*94\*******\*.\*******\*14\**** | ***\*96\*******\*.\*******\*15\**** | ***\*89\*******\*.\*******\*56\**** |

表8显示，在样本分布均衡的条件下，CrossEntropyLoss模型的各项指标表现依然较差，准确率仅为47.25%，AUCROC也只有50.11%，与随机猜测接近，说明其依旧未能有效学习样本的判别边界。而使用DeviationLoss 后，模型在准确率、精度和F1分数方面均有大幅提升，尤其F1分数达到94.14%，AUCROC高达96.15%，说明偏差网络不仅能在不平衡数据中捕捉异常模式，在样本均衡时仍具备强大的分类能力。

**表** **9** **正常数据表现**

| ***\*Model\****            | ***\*A\*******\*ccuracy\*******\*(%)\**** |
| -------------------------- | ----------------------------------------- |
| ***\*CrossEntropyLoss\**** | 47.25                                     |
| ***\*DeviationLoss\****    | ***\*89\*******\*.\*******\*56\****       |

 

**表** **10** **异常数据表现**

| ***\*Model\****            | ***\*A\*******\*ccuracy\*******\*(%)\**** |
| -------------------------- | ----------------------------------------- |
| ***\*CrossEntropyLoss\**** | 47.24                                     |
| ***\*DeviationLoss\****    | ***\*89\*******\*.\*******\*56\****       |

表9和表10分别展示了模型在正常样本和异常样本上的识别准确率。可以看到，CrossEntropyLoss在两个类别上的表现几乎相同，均为47%左右，说明模型无法区分两类数据。而DeviationLoss在两类样本上均取得了89.56%的准确率，进一步证明其在平衡样本分布下对正常与异常样本的判别能力均衡而优秀。这也说明，Z分数偏差机制在平衡数据下不会产生“偏向一方”的问题，反而因为异常和正常样本在数量上势均力敌，使得模型可以更清晰地学习两类数据之间的边界。

### 4.2.3 实验结果及分析总结

***\*(1) 实验结果总结\****

从两个数据集的实验结果来看，基于Z分数的偏差损失函数(DeviationLoss)在整体性能上显著优于传统的交叉熵损失函数(CrossEntropyLoss)，无论是在样本极度不平衡的场景下，还是在样本分布相对均衡的场景中均表现优异。

在Credit Card Fraud Detection数据集中，交叉熵模型几乎完全失效，表现出极低的精度(0.2%)与F1分数(0.4%)，而DeviationLoss则大幅提升了AUC-ROC(98.00%)与AUCPR(70.89%)，证明其在识别极少数诈骗交易时具备显著优势。然而该模型在正常样本的检测能力依然较弱，准确率仅为50.08%，表明其仍存在“过拟合异常类”的倾向。

为此，本文进一步引入VAE生成模型以增强异常样本的多样性，缓解数据稀缺问题。实验表明，VAE+DeviationLoss的模型在精度和F1上均较原始DeviationLoss有所提升，且保持了更高AUC表现，说明伪样本的加入有效增强了模型的泛化能力。

最终，通过集成5个子模型，构建了基于投票机制的集成模型。该模型在综合指标上表现最为优异，在整体准确率(95.78%)、精度(15.82%)和F1分数(26.94%)上远超单模型，同时保留了高AUC表现(AUC-ROC: 97.54%，AUCPR: 85.52%)。尤其在正常数据上的准确率达到95.83%，显著解决了偏差网络对主类识别不强的问题。这表明，集成策略不仅提升了鲁棒性，更弥合了异常类与正常类检测能力之间的差距，具备在实际金融风控场景中部署的可行性。

在Credit Card Fraud Detection Dataset 2023数据集中，DeviationLoss模型同样表现出色，五项指标均显著优于交叉熵，尤其是准确率和F1分数均超过89%。值得注意的是，该场景下模型无需使用VAE扩充数据，说明偏差损失本身已具备较强的适应能力。

### 4.2.4 关于实验结果的深入思考

***\*原始的偏差网络模型并未在样本分布均衡的数据集上进行过系统性实验验证。\****在样本分布均衡的数据集中，基于Z分数构建的偏差损失函数依然展现出优越的性能。我认为，这一现象的根本原因在于：当正常样本与异常样本数量相当时，模型在训练过程中能均衡关注两类样本的异常分数学习。偏差损失的优化目标是使正常样本的异常分数尽可能接近参考分布均值![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml14620\wps66.jpg)，而强制异常样本的分数至少偏离![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml14620\wps67.jpg)达到![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml14620\wps68.jpg)(即Z分数偏差不小于a)。在两类样本数量相当的情况下，模型被迫同时“拉近”正常样本、并“推远”异常样本，相当于拉开了二者在异常评分维度上的分布间距，理论上形成了约![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml14620\wps69.jpg)的最小分数间隔。

这一分数间隔具有显著的统计意义，表示两类样本在异常评分维度上被严格区分开，从而提升了模型对两类样本的可分性。正如DevNet原论文所指出的，偏差损失函数本质上是通过Z分数约束，使异常样本的异常分数落在![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml14620\wps70.jpg)之外，从而在概率意义上保证其显著性水平为![img](file:///C:\Users\<USER>\AppData\Local\Temp\ksohtml14620\wps71.jpg)。在本文实验中使用的a=5对应的显著性水平约为5.73e-7，这意味着模型将异常值压缩至极端尾部，使其与正常样本形成强区分。

相比之下，交叉熵损失函数仅根据标签进行二分类优化，它未明确建模样本在异常评分空间中的统计偏离关系，因此缺乏对异常样本得分“远离正常”的结构性约束，尤其在异常行为高度多样、无法由简单分类边界划分时，其表达能力大打折扣，最终导致性能显著劣于偏差损失。

因此，哪怕在样本数量均衡的情况下，偏差损失依然能够充分利用其统计驱动的判别机制，实现更加可解释、鲁棒且有效的异常检测。



# ***\*5\*******\*. 课程设计心得体会\****

## 5.1 网络设计心得体会

### 5.1.1 偏差网络设计心得

本次课程设计中，我主要使用了Pytorch框架对DevNet进行了复现与实现。由于原始代码采用的是TensorFlow 1.x版本，而我对该框架并不熟悉，因此在最初的转换与实现过程中遇到了许多障碍。尤其是在模型结构、张量维度转换和训练流程构建中，由于框架语法差异明显，我一度陷入调试瓶颈。但通过查阅大量文档与实例代码，并结合自己对Pytorch已有的掌握，最终我成功完成了模型的迁移与重建，这个过程极大锻炼了我将理论模型转化为实际可运行系统的能力。

在实现偏差损失函数的过程中，起初我并不理解为什么基于Z分数的偏差损失函数相较于交叉熵损失函数可以使得模型的效果提升那么大。为此我首先深入了解了均方误差损失函数和交叉熵损失函数的设计原理，认识到均方误差主要通过最小化预测值与真实值之间的平方差来衡量误差，而交叉熵损失函数则基于统计学中的最大似然估计，用于衡量两个概率分布之间的差异。通过比较我逐渐意识到，交叉熵虽然适用于二分类任务，但其在样本极度不平衡的场景下容易出现模型对多数类的过拟合。偏差损失函数的核心思想之一，就是通过增强异常样本在损失计算中的影响力，间接提高它们的权重。偏差损失函数则从统计角度出发，将异常样本强制推离正常分布的尾部，从而增强了模型对异常样本的敏感性，这种通过Z分数控制分布显著性的策略非常新颖。尽管我也尝试设计新的结构性损失函数，最终未能超越偏差损失函数的性能，但这个过程极大加深了我对各类损失函数背后思想的理解，也让我加深了对神经网络的理解。

### 5.1.2 VAE网络设计心得

在样本不平衡问题的解决策略上，我最初设想使用对抗生成网络(GAN)来合成异常样本，期望能构造出更具欺骗性的伪样本。但在实际操作中，由于缺乏高质量的训练样本，且GAN模型训练本身极为不稳定，始终无法收敛，训练过程也难以复现和调优。因此，我放弃了这一思路，转而采用更稳定的生成模型——变分自编码器(VAE)。

VAE作为一种概率生成模型，具备结构简单、易于收敛的优势，更适用于小规模异常样本的学习。通过分析过采样和欠采样的缺陷，我意识到数据级别的“平衡化”更需要从分布角度出发进行建模而非简单复制。VAE恰好能够在潜在空间中捕捉异常样本的分布特征，从而生成与之近似的样本，达到提升训练数据多样性与增强模型鲁棒性的效果。在实验中我观察到，加入VAE生成的异常数据后，模型在识别诈骗样本方面的能力明显增强，也说明我选择这条技术路径是正确的。

### 5.1.3 基于弱分类器的投票集成学习设计心得

在设计这个集成学习分类器时，我最初的想法是将交叉熵损失函数训练出的模型与偏差损失函数训练出的模型进行线性组合。因为交叉熵损失函数能训练出在正常样本上识别率高达99%、但对异常样本几乎无法识别(准确率接近0%)的模型。而偏差损失函数在异常样本上有很强的识别能力(准确率可达98%)，但对正常样本的判断仅约为50%。我设想通过对这两类模型进行线性加权，可以在一定程度上兼顾两类样本的识别效果，从而实现一个既能准确识别正常样本，又不遗漏异常样本的分类器。

然而，实际操作中我低估了这种方法的实现难度：模型输出在数值尺度上并不一致，且很难找到一个统一适配所有样本的线性组合参数。多次尝试未果后，我决定调整思路，我想到了大数定理，即当分类器个数为n时，所有分类总有出现50%的正确样本预测正确，以及98%的异常样本预测正确。因此只要设置异常样本的预测阈值，即可实现样本的准确预测。具体来说，如果我们训练出一组偏差网络模型，它们对正常样本有50%左右的识别准确率、对异常样本的准确率高达98%，那么只要设置一个合理的预测阈值，就可以通过多数投票方式过滤掉大部分误判，实现更可靠的预测效果。这一策略最终成为我后续集成学习设计的关键思路。

在具体实现过程中，我起初的思路是使用全部数据训练一个模型，认为这种方式可以最大程度地提升分类器的泛化能力。接着，我打算通过重复训练相同模型10次来构建多个分类器，以实现集成学习。然而在实际实验中，我发现这样做并未带来任何性能提升，所有模型的预测结果几乎完全一致，和原始单模型的效果没有本质区别。经过分析我意识到，由于每个分类器都使用相同的训练数据，它们对同一输入样本的输出自然也是一致的，因此投票机制无法发挥应有的作用。认识到这一点后，我调整了策略，将完整数据集划分为多个子集，并在每个子集上分别训练一个独立的弱分类器。通过这种方式，每个分类器学到的特征略有差异，从而在集成时提供了更多样化的判断依据。最终，该方法实现了预期的效果，显著提升了模型的整体性能。这个过程中也让我更加深入地理解了集成学习的本质，并不是简单地复制模型，而是引入多样性与差异性，以增强整体的判别能力与鲁棒性。

 

## 5.2 实验心得体会

其实，科研本质上就是一个不断思考与反复实践相结合的过程。我们需要通过分析现有方法的不足，提出自己的改进思路，并通过实验去验证这些想法的可行性。在这个过程中，往往会遇到各种各样的技术难题和意外情况，而正是这些问题的出现与解决，促使我们的能力不断提升，也让书本上的理论知识在实践中得到了更深刻的理解与巩固。

另外，我也逐渐意识到，科研要敢于提出问题，勇于尝试新路径，并从实验结果中寻找解释。在4.2.4节中，我基于自己构建的偏差损失模型，在均衡数据集上开展新一轮实验，本以为表现会一般，但却得到了比预期更好的结果。我通过深入分析模型在该数据集上表现良好的原因，进一步理解了偏差网络的判别机制和Z分数在概率意义上的显著性控制作用。这一尝试让我明白，科研的每一次新尝试都有可能带来认知的突破，而不是仅仅复制已有的结论。



***\*参考文献\****

[1] 杨涛, 高峰, 吕仲涛, et al. 数字金融发展现状与展望 [J]. 金融理论探索, 2022, 01): 30-8.

[2] 高春宝. 提升金融机构反电信网络诈骗能力 [J]. 中国金融, 2025, 05): 90-1.

[3] 姜其林, 陈焕雷, 刘文. AI在智慧银行运营中的数据分析与客户行为预测研究 [J]. 金融科技时代, 2025, 33(03): 11-5.

[4] PANG G, SHEN C, VAN DEN HENGEL A. Deep anomaly detection with deviation networks; proceedings of the Proceedings of the 25th ACM SIGKDD international conference on knowledge discovery & data mining, F, 2019 [C].

[5] 侯赛, 成润坤, 刘达. 基于二次采样和集成学习方法的变压器故障预测 [J]. 智慧电力, 2024, 52(07): 40-7.

[6] KINGMA D P, WELLING M. Auto-encoding variational bayes [M]. Banff, Canada. 2013.

 



