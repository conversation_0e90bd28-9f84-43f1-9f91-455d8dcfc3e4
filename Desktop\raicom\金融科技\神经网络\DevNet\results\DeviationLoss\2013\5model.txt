PS G:\Curriculum_Design\DevNet> python .\ensemble_train_5models.py
开始集成训练: creditcardfraud_normalised
加载完整训练数据: 252724 样本
诈骗样本: 25272, 正常样本: 227452
加载测试数据: 57355 样本
测试集诈骗样本: 492.0, 正常样本: 56863.0

将训练数据分成5份...

=== 训练模型 1/5 ===
模型 1 训练数据: 50546 样本
诈骗样本: 5055, 正常样本: 45491
模型 1, Epoch 0, Loss: 2.5057
模型 1, Epoch 10, Loss: 0.3728
模型 1, Epoch 20, Loss: 0.3016
模型 1, Epoch 30, Loss: 0.3046
模型 1, Epoch 40, Loss: 0.2943
模型 1 已保存到: ./model\creditcardfraud_normalised\ensemble_5models\DeviationLoss_512bs_50ep_4d_model1.pt

=== 测试模型 1 ===
AUC-ROC: 0.9755, AUC-PR: 0.8542
AUC-ROC: 0.9755, AUC-PR: 0.8542
阈值: 0.0059
整个测试集 - 准确率: 0.5083, 精度: 0.0168, F1: 0.0331
正常数据 (56863样本) - 准确率: 0.5042, 精度: 1.0000, F1: 0.6704
异常数据 (492样本) - 准确率: 0.9817, 精度: 1.0000, F1: 0.9908

=== 训练模型 2/5 ===
模型 2 训练数据: 50546 样本
诈骗样本: 5055, 正常样本: 45491
模型 2, Epoch 0, Loss: 1.8338
模型 2, Epoch 10, Loss: 0.3427
模型 2, Epoch 20, Loss: 0.3059
模型 2, Epoch 30, Loss: 0.3339
模型 2, Epoch 40, Loss: 0.2745
模型 2 已保存到: ./model\creditcardfraud_normalised\ensemble_5models\DeviationLoss_512bs_50ep_4d_model2.pt

=== 测试模型 2 ===
AUC-ROC: 0.9760, AUC-PR: 0.8548
AUC-ROC: 0.9760, AUC-PR: 0.8548
阈值: -0.0187
整个测试集 - 准确率: 0.5082, 精度: 0.0168, F1: 0.0330
正常数据 (56863样本) - 准确率: 0.5042, 精度: 1.0000, F1: 0.6704
异常数据 (492样本) - 准确率: 0.9797, 精度: 1.0000, F1: 0.9897

=== 训练模型 3/5 ===
模型 3 训练数据: 50544 样本
诈骗样本: 5054, 正常样本: 45490
模型 3, Epoch 0, Loss: 1.7660
模型 3, Epoch 10, Loss: 0.3651
模型 3, Epoch 20, Loss: 0.3510
模型 3, Epoch 30, Loss: 0.2842
模型 3, Epoch 40, Loss: 0.2775
模型 3 已保存到: ./model\creditcardfraud_normalised\ensemble_5models\DeviationLoss_512bs_50ep_4d_model3.pt

=== 测试模型 3 ===
AUC-ROC: 0.9242, AUC-PR: 0.8506
AUC-ROC: 0.9242, AUC-PR: 0.8506
阈值: 0.0226
整个测试集 - 准确率: 0.5074, 精度: 0.0158, F1: 0.0311
正常数据 (56863样本) - 准确率: 0.5038, 精度: 1.0000, F1: 0.6701
异常数据 (492样本) - 准确率: 0.9228, 精度: 1.0000, F1: 0.9598

=== 训练模型 4/5 ===
模型 4 训练数据: 50544 样本
诈骗样本: 5054, 正常样本: 45490
模型 4, Epoch 0, Loss: 2.5121
模型 4, Epoch 10, Loss: 0.3307
模型 4, Epoch 20, Loss: 0.3067
模型 4, Epoch 30, Loss: 0.2666
模型 4, Epoch 40, Loss: 0.3677
模型 4 已保存到: ./model\creditcardfraud_normalised\ensemble_5models\DeviationLoss_512bs_50ep_4d_model4.pt

=== 测试模型 4 ===
AUC-ROC: 0.9752, AUC-PR: 0.8544
AUC-ROC: 0.9752, AUC-PR: 0.8544
阈值: -0.0051
整个测试集 - 准确率: 0.5082, 精度: 0.0168, F1: 0.0330
正常数据 (56863样本) - 准确率: 0.5042, 精度: 1.0000, F1: 0.6704
异常数据 (492样本) - 准确率: 0.9797, 精度: 1.0000, F1: 0.9897

=== 训练模型 5/5 ===
模型 5 训练数据: 50544 样本
诈骗样本: 5054, 正常样本: 45490
模型 5, Epoch 0, Loss: 1.8632
模型 5, Epoch 10, Loss: 0.3722
模型 5, Epoch 20, Loss: 0.3375
模型 5, Epoch 30, Loss: 0.2863
模型 5, Epoch 40, Loss: 0.3132
模型 5 已保存到: ./model\creditcardfraud_normalised\ensemble_5models\DeviationLoss_512bs_50ep_4d_model5.pt

=== 测试模型 5 ===
AUC-ROC: 0.9739, AUC-PR: 0.8563
AUC-ROC: 0.9739, AUC-PR: 0.8563
阈值: -0.0243
整个测试集 - 准确率: 0.5082, 精度: 0.0168, F1: 0.0330
正常数据 (56863样本) - 准确率: 0.5041, 精度: 1.0000, F1: 0.6703
异常数据 (492样本) - 准确率: 0.9776, 精度: 1.0000, F1: 0.9887

=== 5个模型训练完成 ===
总训练时间: 260.22 秒
总测试时间: 1.15 秒

=== 集成预测结果 ===
使用投票规则: 5个以上模型预测为诈骗则为诈骗
预测为诈骗的数量: 2819
预测为正常的数量: 54536
AUC-ROC: 0.9754, AUC-PR: 0.8552

=== 集成模型 - 整个测试集指标 ===
AUC-ROC: 0.9754
AUC-PR: 0.8552
准确率: 0.9578
精度: 0.1582
F1分数: 0.2694

=== 集成模型 - 正常数据指标 ===
样本数: 56863
准确率: 0.9583
精度: 1.0000
F1分数: 0.9787

=== 集成模型 - 异常数据指标 ===
样本数: 492
准确率: 0.9065
精度: 1.0000
F1分数: 0.9510
==================================================

=== 各个模型AUC结果 ===
模型 1: AUC-ROC=0.9755, AUC-PR=0.8542
模型 2: AUC-ROC=0.9760, AUC-PR=0.8548
模型 3: AUC-ROC=0.9242, AUC-PR=0.8506
模型 4: AUC-ROC=0.9752, AUC-PR=0.8544
模型 5: AUC-ROC=0.9739, AUC-PR=0.8563

=== 最终集成结果 ===
集成 AUC-ROC: 0.9754
集成 AUC-PR: 0.8552
总训练时间: 260.22 秒
总测试时间: 1.15 秒

集成预测结果已保存到: ./results\ensemble_5models_creditcardfraud_normalised_vote4.npz