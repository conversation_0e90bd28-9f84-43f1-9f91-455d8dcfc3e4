import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from torch.utils.data import DataLoader, TensorDataset
import pandas as pd
from sklearn.preprocessing import StandardScaler

class FraudVAE(nn.Module):
    
    def __init__(self, input_dim, latent_dim=20, hidden_dim=128):
        super(FraudVAE, self).__init__()
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim//2),
            nn.ReLU()
        )
        
        self.fc_mu = nn.Linear(hidden_dim//2, latent_dim)
        self.fc_logvar = nn.Linear(hidden_dim//2, latent_dim)
        
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, hidden_dim//2),
            nn.ReLU(),
            nn.Linear(hidden_dim//2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, input_dim)
        )
        
    def encode(self, x):
        h = self.encoder(x)
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        return mu, logvar
    
    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def decode(self, z):
        return self.decoder(z)
    
    def forward(self, x):
        mu, logvar = self.encode(x)
        z = self.reparameterize(mu, logvar)
        recon_x = self.decode(z)
        return recon_x, mu, logvar
    
    def generate_samples(self, n_samples, device):
        self.eval()
        with torch.no_grad():
            z = torch.randn(n_samples, self.latent_dim).to(device)
            generated = self.decode(z)
        return generated.cpu().numpy()

def vae_loss(recon_x, x, mu, logvar, beta=1.0):
    """VAE损失函数"""
    # 重构损失
    recon_loss = F.mse_loss(recon_x, x, reduction='sum')
    
    # KL散度损失
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    
    return recon_loss + beta * kl_loss

class FraudDataGenerator:
    """诈骗数据生成器"""
    
    def __init__(self, input_dim, device='cpu'):
        self.device = device
        self.vae = FraudVAE(input_dim).to(device)
        self.scaler = StandardScaler()
        self.is_fitted = False
        
    def fit(self, fraud_data, epochs=100, batch_size=32, lr=1e-3):
        """训练VAE模型学习诈骗数据分布"""
        print(f"开始训练VAE模型，诈骗样本数: {len(fraud_data)}")
        
        # 数据标准化
        fraud_data_scaled = self.scaler.fit_transform(fraud_data)
        
        # 创建数据加载器
        dataset = TensorDataset(torch.FloatTensor(fraud_data_scaled))
        dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
        
        # 优化器
        optimizer = torch.optim.Adam(self.vae.parameters(), lr=lr)
        
        # 训练循环
        self.vae.train()
        for epoch in range(epochs):
            total_loss = 0
            for batch_idx, (data,) in enumerate(dataloader):
                data = data.to(self.device)
                
                optimizer.zero_grad()
                recon_data, mu, logvar = self.vae(data)
                loss = vae_loss(recon_data, data, mu, logvar)
                loss.backward()
                optimizer.step()
                
                total_loss += loss.item()
            
            if epoch % 20 == 0:
                avg_loss = total_loss / len(dataloader)
                print(f'Epoch {epoch}, Average Loss: {avg_loss:.4f}')
        
        self.is_fitted = True
        print("VAE模型训练完成")
    
    def generate_fraud_samples(self, n_samples):
        """生成指定数量的诈骗样本"""
        if not self.is_fitted:
            raise ValueError("模型尚未训练，请先调用fit方法")
        
        print(f"生成 {n_samples} 个合成诈骗样本")
        generated_scaled = self.vae.generate_samples(n_samples, self.device)
        
        # 反标准化
        generated_samples = self.scaler.inverse_transform(generated_scaled)
        
        return generated_samples