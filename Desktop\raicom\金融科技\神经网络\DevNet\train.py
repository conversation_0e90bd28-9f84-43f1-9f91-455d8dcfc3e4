import numpy as np
import torch
import torch.optim as optim
import torch.nn as nn
from torch.utils.data import DataLoader
import argparse
import sys
import os
import time
from scipy.sparse import vstack
from sklearn.model_selection import train_test_split

from improved_loss import *
from model import create_network, DeviationLoss, get_device
from utils import dataLoading, aucPerformance, writeResults, get_data_from_svmlight_file, DevNetDataset, inject_noise, inject_noise_sparse

# 设置随机种子
np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed(42)

def run_devnet(args):
    names = args.data_set.split(',')
    network_depth = int(args.network_depth)
    random_seed = args.ramdn_seed
    device = get_device()
    
    for nm in names:
        runs = args.runs
        rauc = np.zeros(runs)
        ap = np.zeros(runs)  
        filename = nm.strip()
        data_format = int(args.data_format)
        
        if data_format == 0:
            x, labels = dataLoading(args.input_path + filename + ".csv")
        else:
            x, labels = get_data_from_svmlight_file(args.input_path + filename + ".svm")
            x = x.tocsr()    
        
        outlier_indices = np.where(labels == 1)[0]
        outliers = x[outlier_indices]  
        n_outliers_org = outliers.shape[0]   
        
        train_time = 0
        test_time = 0
        
        for i in np.arange(runs):  
            x_train, x_test, y_train, y_test = train_test_split(x, labels, test_size=0.2, random_state=42, stratify=labels)
            y_train = np.array(y_train)
            y_test = np.array(y_test)
            
            print(filename + ': round ' + str(i))
            outlier_indices = np.where(y_train == 1)[0]
            inlier_indices = np.where(y_train == 0)[0]
            n_outliers = len(outlier_indices)
            print("Original training size: %d, No. outliers: %d" % (x_train.shape[0], n_outliers))
            
            n_noise = len(np.where(y_train == 0)[0]) * args.cont_rate / (1. - args.cont_rate)
            n_noise = int(n_noise)                
            
            rng = np.random.RandomState(random_seed)  
            
            if data_format == 0:                
                if n_outliers > args.known_outliers:
                    mn = n_outliers - args.known_outliers
                    remove_idx = rng.choice(outlier_indices, mn, replace=False)            
                    x_train = np.delete(x_train, remove_idx, axis=0)
                    y_train = np.delete(y_train, remove_idx, axis=0)
                
                noises = inject_noise(outliers, n_noise, random_seed)
                x_train = np.append(x_train, noises, axis=0)
                y_train = np.append(y_train, np.zeros((noises.shape[0], 1)))
            
            else:
                if n_outliers > args.known_outliers:
                    mn = n_outliers - args.known_outliers
                    remove_idx = rng.choice(outlier_indices, mn, replace=False)        
                    retain_idx = set(np.arange(x_train.shape[0])) - set(remove_idx)
                    retain_idx = list(retain_idx)
                    x_train = x_train[retain_idx]
                    y_train = y_train[retain_idx]                               
                
                noises = inject_noise_sparse(outliers, n_noise, random_seed)
                x_train = vstack([x_train, noises])
                y_train = np.append(y_train, np.zeros((noises.shape[0], 1)))
            
            outlier_indices = np.where(y_train == 1)[0]
            inlier_indices = np.where(y_train == 0)[0]
            print(y_train.shape[0], outlier_indices.shape[0], inlier_indices.shape[0], n_noise)
            
            if data_format == 1:
                x_train = x_train.toarray()
                x_test = x_test.toarray()
            
            input_dim = x_train.shape[1]
            n_samples_trn = x_train.shape[0]
            n_outliers = len(outlier_indices)            
            print("Training data size: %d, No. outliers: %d" % (x_train.shape[0], n_outliers))
            
            # 创建数据集和数据加载器
            train_dataset = DevNetDataset(x_train, outlier_indices, inlier_indices)
            train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
            
            # 创建模型
            model = create_network(input_dim, network_depth).to(device)
            criterion = nn.CrossEntropyLoss().to(device)
            # criterion = DeviationLoss().to(device)
            optimizer = optim.RMSprop(model.parameters(), lr=0.001, weight_decay=model.weight_decay if hasattr(model, 'weight_decay') else 0)
            
            # 训练模型
            start_time = time.time()
            model.train()
            
            for epoch in range(args.epochs):
                running_loss = 0.0
                for batch_idx, (data, target) in enumerate(train_loader):
                    if batch_idx >= args.nb_batch:
                        break
                        
                    data, target = data.to(device), target.to(device)
                    
                    optimizer.zero_grad()
                    outputs = model(data)
                    loss = criterion(outputs, target)
                    loss.backward()
                    optimizer.step()
                    
                    running_loss += loss.item()
                    
                if epoch % 10 == 0:
                    print(f'Epoch {epoch}, Loss: {running_loss / args.nb_batch}')
            
            
            train_time += time.time() - start_time
            
            # 保存模型
            model_path = f"./model/devnet_{filename}_{args.cont_rate}cr_{args.batch_size}bs_{args.known_outliers}ko_{network_depth}d.pt"
            os.makedirs(os.path.dirname(model_path), exist_ok=True)
            torch.save(model.state_dict(), model_path)
            
            # 测试模型
            start_time = time.time()
            model.eval()
            
            with torch.no_grad():
                x_test_tensor = torch.FloatTensor(x_test).to(device)
                scores = model(x_test_tensor).cpu().numpy()
            
            test_time += time.time() - start_time
            rauc[i], ap[i] = aucPerformance(scores, y_test)
            
            # 计算分类指标
            from sklearn.metrics import accuracy_score, precision_score, f1_score
            
            # 使用分数的中位数作为阈值
            threshold = np.median(scores)
            y_pred = (scores > threshold).astype(int)
            
            # 1. 整个测试集的指标
            overall_accuracy = accuracy_score(y_test, y_pred)
            overall_precision = precision_score(y_test, y_pred, zero_division=0)
            overall_f1 = f1_score(y_test, y_pred, zero_division=0)
            
            print(f"\n=== 整个测试集指标 ===")
            print(f"准确率: {overall_accuracy:.4f}")
            print(f"精度: {overall_precision:.4f}")
            print(f"F1分数: {overall_f1:.4f}")
            
            # 2. 只有正常数据的指标
            normal_indices = np.where(y_test == 0)[0]
            if len(normal_indices) > 0:
                normal_pred = y_pred[normal_indices]
                normal_true = y_test[normal_indices]
                
                normal_accuracy = accuracy_score(normal_true, normal_pred)
                # 对于正常数据，精度计算需要反转标签（0变1，1变0）
                normal_precision = precision_score(1-normal_true, 1-normal_pred, zero_division=0)
                normal_f1 = f1_score(1-normal_true, 1-normal_pred, zero_division=0)
                
                print(f"\n=== 正常数据指标 ===")
                print(f"样本数: {len(normal_indices)}")
                print(f"准确率: {normal_accuracy:.4f}")
                print(f"精度: {normal_precision:.4f}")
                print(f"F1分数: {normal_f1:.4f}")
            
            # 3. 只有异常数据的指标
            anomaly_indices = np.where(y_test == 1)[0]
            if len(anomaly_indices) > 0:
                anomaly_pred = y_pred[anomaly_indices]
                anomaly_true = y_test[anomaly_indices]
                
                anomaly_accuracy = accuracy_score(anomaly_true, anomaly_pred)
                anomaly_precision = precision_score(anomaly_true, anomaly_pred, zero_division=0)
                anomaly_f1 = f1_score(anomaly_true, anomaly_pred, zero_division=0)
                
                print(f"\n=== 异常数据指标 ===")
                print(f"样本数: {len(anomaly_indices)}")
                print(f"准确率: {anomaly_accuracy:.4f}")
                print(f"精度: {anomaly_precision:.4f}")
                print(f"F1分数: {anomaly_f1:.4f}")
            
            print("=" * 50)
        
        mean_auc = np.mean(rauc)
        std_auc = np.std(rauc)
        mean_aucpr = np.mean(ap)
        std_aucpr = np.std(ap)
        train_time = train_time/runs
        test_time = test_time/runs
        print("average AUC-ROC: %.4f, average AUC-PR: %.4f" % (mean_auc, mean_aucpr))    
        print("average runtime: %.4f seconds" % (train_time + test_time))
        writeResults(filename+'_'+str(network_depth), x.shape[0], x.shape[1], n_samples_trn, n_outliers_org, n_outliers,
                     network_depth, mean_auc, mean_aucpr, std_auc, std_aucpr, train_time, test_time, path=args.output)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--network_depth", choices=['1','2', '4'], default='4', help="the depth of the network architecture")
    parser.add_argument("--batch_size", type=int, default=512, help="batch size used in SGD")
    parser.add_argument("--nb_batch", type=int, default=20, help="the number of batches per epoch")
    parser.add_argument("--epochs", type=int, default=50, help="the number of epochs")
    parser.add_argument("--runs", type=int, default=1, help="how many times we repeat the experiments to obtain the average performance")
    parser.add_argument("--known_outliers", type=int, default=30, help="the number of labeled outliers available at hand")
    parser.add_argument("--cont_rate", type=float, default=0.02, help="the outlier contamination rate in the training data")
    parser.add_argument("--input_path", type=str, default='./dataset/', help="the path of the data sets")
    parser.add_argument("--data_set", type=str, default='creditcardfraud_normalised', help="a list of data set names")
    parser.add_argument("--data_format", choices=['0','1'], default='0',  help="specify whether the input data is a csv (0) or libsvm (1) data format")
    parser.add_argument("--output", type=str, default='./results/devnet_auc_performance_30outliers_0.02contrate_2depth_10runs.csv', help="the output file path")
    parser.add_argument("--ramdn_seed", type=int, default=42, help="the random seed number")
    args = parser.parse_args()
    run_devnet(args)