import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np



class AUCPRLoss(nn.Module):

    def __init__(self, ref_size=5000):
        super(AUCPRLoss, self).__init__()
        self.confidence_margin = 5.0
        self.register_buffer('ref_dist', torch.FloatTensor(np.random.normal(loc=0., scale=1.0, size=ref_size)))
        
    def forward(self, y_pred, y_true):
        dev = (y_pred - torch.mean(self.ref_dist)) / torch.std(self.ref_dist)
        
        pos_mask = y_true == 1
        neg_mask = y_true == 0
        
        if torch.sum(pos_mask) == 0 or torch.sum(neg_mask) == 0:
            inlier_loss = torch.abs(dev)
            outlier_loss = torch.abs(torch.clamp(self.confidence_margin - dev, min=0.))
            return torch.mean((1 - y_true) * inlier_loss + y_true * outlier_loss)
        
        pos_scores = dev[pos_mask]
        neg_scores = dev[neg_mask]
        
        pos_scores_expanded = pos_scores.unsqueeze(1)  # [n_pos, 1]
        neg_scores_expanded = neg_scores.unsqueeze(0)  # [1, n_neg]
        
        score_diff = pos_scores_expanded - neg_scores_expanded  # [n_pos, n_neg]
        
        pairwise_loss = torch.sigmoid(-score_diff)
        weighted_pairwise_loss = pairwise_loss
        
        ranking_loss = torch.mean(weighted_pairwise_loss)
        
        inlier_loss = torch.abs(dev)
        outlier_loss = torch.abs(torch.clamp(self.confidence_margin - dev, min=0.))
        
        return ranking_loss