# 基于偏差网络的金融诈骗检测系统

## 项目简介

本项目是一个基于深度学习的金融诈骗检测系统，采用偏差网络(DevNet)作为核心算法，专门用于信用卡交易中的欺诈检测。该系统结合了变分自编码器(VAE)数据生成技术和集成学习策略，有效解决了金融数据中类别极度不平衡的问题。

## 核心特性

- **偏差网络(DevNet)**: 专门针对异常检测设计的深度神经网络
- **基于Z分数的偏差损失函数**: 创新的损失函数，专门处理类别不平衡问题
- **VAE数据生成**: 生成高质量的合成诈骗样本，缓解数据稀缺问题
- **集成学习策略**: 5个弱分类器投票机制，提高预测稳定性
- **多种网络架构**: 支持1层、2层、4层不同深度的网络结构
- **多种标准化方法**: 支持MinMax、Z-score、对数变换、分位数变换

## 技术架构

### 1. 偏差网络(DevNet)
- **深层架构**: 1000→250→20→1的四层前馈神经网络
- **激活函数**: ReLU激活函数
- **输出**: 直接输出异常得分(标量值)

### 2. 偏差损失函数
```python
dev(x) = Φ(x; θ) − μR
L = (1 − y) · |dev(x)| + y · max(0, α − dev(x))
```
- **α**: Z分数显著性阈值(默认为5)
- **μR, σR**: 参考分布的均值和标准差

### 3. VAE生成模型
- **编码器**: 输入→128→64→潜在空间(20维)
- **解码器**: 潜在空间→64→128→输出
- **损失函数**: 重构损失 + KL散度损失

### 4. 集成学习
- **策略**: 5个弱分类器投票
- **投票规则**: 严格多数投票(5个模型全部预测为诈骗才判定为诈骗)

## 环境要求

### Python版本
- Python 3.7+

### 依赖包
```bash
torch>=1.8.0
numpy>=1.19.0
pandas>=1.2.0
scikit-learn>=0.24.0
scipy>=1.6.0
joblib>=1.0.0
```

## 安装指南

1. **克隆项目**
```bash
git clone <repository-url>
cd DevNet
```

2. **安装依赖**
```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install torch numpy pandas scikit-learn scipy joblib matplotlib seaborn
```

3. **创建必要目录**
```bash
mkdir -p dataset model results normalize
```

## 数据集

### 支持的数据集
1. **Credit Card Fraud Detection (2013)**
   - 来源: Kaggle
   - 样本数: 284,807条交易记录
   - 诈骗率: 0.172% (极度不平衡)
   - 特征: V1-V28 (PCA处理后), Time, Amount, Class

2. **Credit Card Fraud Detection Dataset 2023**
   - 来源: Kaggle
   - 样本数: 568,630条交易记录
   - 诈骗率: 50% (平衡数据集)
   - 特征: id, V1-V28, Amount, Class

### 数据格式
- **CSV格式**: 标准CSV文件，最后一列为标签(class)
- **LibSVM格式**: 稀疏数据格式

## 快速开始

以下是一个完整的使用示例：

```bash
# 1. 准备数据集 (将creditcard.csv放入dataset目录)
cp your_creditcard_data.csv ./dataset/creditcard.csv

# 2. 数据标准化
python normalize.py --input_file ./dataset/creditcard.csv \
                   --output_file ./dataset/creditcard_normalized.csv \
                   --method minmax --skip_columns Class

# 3. 生成训练数据 (使用VAE生成合成样本)
python generate_data.py --data_set creditcard_normalized \
                       --input_path ./dataset/ \
                       --data_format 0

# 4. 训练集成模型
python ensemble_train_5models.py --data_set creditcard_normalized \
                                 --network_depth 4 \
                                 --epochs 50

# 5. 查看结果
ls ./results/
```

## 使用指南

### 1. 数据预处理
```bash
# 标准化数据
python normalize.py --input_file ./dataset/creditcard.csv \
                   --output_file ./dataset/creditcard_normalized.csv \
                   --method minmax \
                   --skip_columns Class
```

### 2. 生成训练数据
```bash
# 使用VAE生成合成诈骗样本
python generate_data.py --data_set creditcardfraud_normalised \
                       --input_path ./dataset/ \
                       --data_format 0 \
                       --runs 1
```

### 3. 训练单个模型
```bash
# 训练DevNet模型
python train.py --network_depth 4 \
               --batch_size 512 \
               --epochs 50 \
               --data_set creditcardfraud_normalised \
               --data_format 0
```

### 4. 训练集成模型
```bash
# 训练5个弱分类器集成模型
python ensemble_train_5models.py --network_depth 4 \
                                 --batch_size 512 \
                                 --epochs 50 \
                                 --data_set creditcardfraud_normalised
```

### 5. 使用生成数据训练
```bash
# 使用VAE生成的数据训练模型
python train_from_generated_data.py --network_depth 4 \
                                   --batch_size 512 \
                                   --epochs 50 \
                                   --data_set creditcardfraud_normalised_2013
```

## 参数配置

### 网络参数
- `network_depth`: 网络深度 (1, 2, 4)
- `batch_size`: 批次大小 (默认: 512)
- `epochs`: 训练轮数 (默认: 50)
- `nb_batch`: 每轮最大批次数 (默认: 20)

### 优化参数
- `learning_rate`: 学习率 (默认: 0.001)
- `weight_decay`: L2正则化系数 (默认: 0.01)
- `optimizer`: 优化器 (RMSprop)

### 损失函数参数
- `confidence_margin`: Z分数阈值 (默认: 5.0)
- `ref_size`: 参考分布大小 (默认: 5000)

## 项目结构

```
DevNet/
├── README.md                           # 项目说明文档
├── 基于偏差网络的金融诈骗检测.md        # 详细技术文档
├── model.py                            # 网络模型定义
├── train.py                            # 单模型训练脚本
├── ensemble_train_5models.py           # 集成模型训练脚本
├── train_from_generated_data.py        # 生成数据训练脚本
├── generate_data.py                    # 数据生成脚本
├── fraud_generator.py                  # VAE生成器
├── improved_loss.py                    # 改进损失函数
├── utils.py                            # 工具函数
├── normalize.py                        # 数据标准化脚本
├── dataset/                            # 数据集目录
├── model/                              # 模型保存目录
├── results/                            # 结果输出目录
└── normalize/                          # 标准化数据目录
```

## 实验结果

### Credit Card Fraud Detection (2013) 数据集

| 模型 | 准确率(%) | 精度(%) | F1分数(%) | AUC-ROC(%) | AUC-PR(%) |
|------|-----------|---------|-----------|------------|-----------|
| CrossEntropyLoss | 50.04 | 0.20 | 0.40 | 56.54 | 17.97 |
| DeviationLoss | 50.17 | 0.34 | 0.68 | **98.00** | 70.89 |
| VAE+DeviationLoss | 50.82 | 1.68 | 3.30 | 97.55 | 85.42 |
| **集成模型** | **95.78** | **15.82** | **26.94** | 97.54 | **85.52** |

### Credit Card Fraud Detection Dataset 2023

| 模型 | 准确率(%) | 精度(%) | F1分数(%) | AUC-ROC(%) | AUC-PR(%) |
|------|-----------|---------|-----------|------------|-----------|
| CrossEntropyLoss | 47.25 | 47.25 | 45.90 | 50.11 | 47.24 |
| **DeviationLoss** | **89.56** | **89.56** | **94.14** | **96.15** | **89.56** |

## 核心优势

1. **处理极度不平衡数据**: 专门设计的偏差损失函数有效处理诈骗检测中的类别不平衡问题
2. **端到端学习**: 直接学习异常得分，避免传统两阶段方法的局限性
3. **数据增强**: VAE生成高质量合成样本，提升模型泛化能力
4. **集成学习**: 多模型投票机制提高预测稳定性和准确性
5. **统计显著性**: 基于Z分数的损失函数具有明确的统计学意义

## 技术创新

1. **偏差损失函数**: 基于Z分数的创新损失函数，将异常样本推离正态分布尾部
2. **VAE数据生成**: 学习诈骗样本分布特征，生成高质量合成数据
3. **严格投票机制**: 5个模型全部预测为诈骗才判定为诈骗，降低误报率
4. **多架构支持**: 支持不同深度的网络架构，适应不同复杂度的数据

## 应用场景

- **信用卡欺诈检测**: 主要应用场景
- **金融风险控制**: 可扩展到其他金融异常检测
- **不平衡数据分类**: 适用于各种类别不平衡的分类问题
- **异常检测**: 通用的异常检测框架

## 贡献者

本项目基于DevNet原始论文实现，使用PyTorch框架重构，并加入了VAE数据生成和集成学习等创新技术。

## 参考文献

1. PANG G, SHEN C, VAN DEN HENGEL A. Deep anomaly detection with deviation networks. KDD 2019.
2. KINGMA D P, WELLING M. Auto-encoding variational bayes. ICLR 2014.
3. 杨涛, 高峰, 吕仲涛, et al. 数字金融发展现状与展望. 金融理论探索, 2022.

## 许可证

本项目仅供学术研究使用。

## 常见问题

### Q1: 如何准备数据集？
A: 将CSV格式的数据集放入`./dataset/`目录，确保最后一列为标签列(0表示正常，1表示诈骗)。

### Q2: 训练时显存不足怎么办？
A: 可以减小`batch_size`参数，例如从512改为256或128。

### Q3: 如何调整模型性能？
A: 可以尝试以下方法：
- 调整网络深度(`network_depth`)
- 修改训练轮数(`epochs`)
- 调整学习率和正则化参数
- 使用不同的标准化方法

### Q4: 集成模型训练失败？
A: 确保先运行`generate_data.py`生成训练数据，再运行集成训练脚本。

## 故障排除

### 1. 导入错误
```bash
# 确保所有依赖包已安装
pip install -r requirements.txt
```

### 2. 数据路径错误
检查数据集路径是否正确，确保文件存在于指定位置。

### 3. CUDA相关错误
如果没有GPU，模型会自动使用CPU训练，但速度较慢。

### 4. 内存不足
减小批次大小或使用数据采样来减少内存使用。

## 性能优化建议

1. **使用GPU加速**: 如有NVIDIA GPU，安装CUDA版本的PyTorch
2. **数据预处理**: 使用适当的标准化方法提升模型性能
3. **超参数调优**: 根据数据集特点调整网络深度和训练参数
4. **早停机制**: 监控验证集性能，避免过拟合

## 扩展功能

### 自定义损失函数
可以在`improved_loss.py`中添加新的损失函数实现。

### 新的网络架构
在`model.py`中可以定义新的网络结构。

### 数据增强
可以扩展VAE生成器，支持更多类型的数据增强。

## 版本历史

- **v1.0**: 基础DevNet实现
- **v1.1**: 添加VAE数据生成功能
- **v1.2**: 集成学习策略实现
- **v1.3**: 多种损失函数支持

## 致谢

感谢原始DevNet论文作者的开创性工作，以及PyTorch社区提供的优秀深度学习框架。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
