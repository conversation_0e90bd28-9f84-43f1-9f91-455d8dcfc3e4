import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler, StandardScaler, QuantileTransformer
import argparse

def normalize_csv(input_file, output_file, method='minmax', columns=None, skip_columns=None, drop_columns=None, decimal_places=6):
    """
    对CSV文件进行标准化处理，支持删除指定列并控制小数位数

    参数:
    input_file (str): 输入CSV文件路径
    output_file (str): 输出CSV文件路径
    method (str): 标准化方法，可选 'minmax', 'zscore', 'log', 'quantile'
    columns (list): 需要标准化的列名列表，默认为None表示所有列
    skip_columns (list): 需要跳过标准化的列名列表，默认为None
    drop_columns (list): 需要删除的列名列表，默认为None
    decimal_places (int): 保留的小数位数，默认为6位
    """
    # 读取CSV文件
    df = pd.read_csv(input_file)

    # 删除指定列（若有）
    if drop_columns:
        existing_columns = [col for col in drop_columns if col in df.columns]
        if existing_columns:
            df = df.drop(columns=existing_columns)
            print(f"已删除列: {existing_columns}")
        else:
            print("未找到要删除的列")

    # 确定需要标准化的列
    all_columns = df.columns.tolist()

    if skip_columns:
        selected_columns = [col for col in all_columns if col not in skip_columns]
    elif columns:
        selected_columns = columns
    else:
        selected_columns = all_columns

    print(f"将对以下列进行{method}标准化: {selected_columns}")

    # 根据选择的方法进行标准化
    if method == 'minmax':
        scaler = MinMaxScaler()
        df[selected_columns] = scaler.fit_transform(df[selected_columns])

    elif method == 'zscore':
        scaler = StandardScaler()
        df[selected_columns] = scaler.fit_transform(df[selected_columns])

    elif method == 'log':
        for col in selected_columns:
            if df[col].min() <= 0:
                df[col] = np.log(df[col] + abs(df[col].min()) + 1)
            else:
                df[col] = np.log(df[col])

    elif method == 'quantile':
        scaler = QuantileTransformer(output_distribution='normal')
        df[selected_columns] = scaler.fit_transform(df[selected_columns])

    else:
        raise ValueError(f"不支持的标准化方法: {method}")

    # 保留指定小数位数（仅对数值列生效）
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    df[numeric_columns] = df[numeric_columns].round(decimal_places)

    # 保存结果
    df.to_csv(output_file, index=False)
    print(f"标准化后的数据已保存至: {output_file}")
    print(f"数值列已保留{decimal_places}位小数")

    return df

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='对CSV文件进行标准化处理，支持控制小数位数')
    parser.add_argument('--input_file', default='./dataset/creditcard_2023.csv', type=str, help='输入CSV文件路径')
    parser.add_argument('--output_file', default='./normalize/creditcard_2023_normalize.csv', type=str, help='输出CSV文件路径')
    parser.add_argument('--method', type=str, default='minmax',
                        choices=['minmax', 'zscore', 'log', 'quantile'],
                        help='标准化方法，默认为minmax')
    parser.add_argument('--columns', nargs='+', type=str, default=None,
                        help='需要标准化的列名列表，用空格分隔')
    parser.add_argument('--skip_columns', nargs='+', type=str, default='Class',
                        help='需要跳过标准化的列名列表，用空格分隔')
    parser.add_argument('--drop_columns', nargs='+', type=str, default=['id'],
                        help='需要删除的列名列表，用空格分隔，默认为不删除')
    parser.add_argument('--decimal_places', type=int, default=6,
                        help='保留的小数位数，默认为6位')

    args = parser.parse_args()

    normalize_csv(
        args.input_file,
        args.output_file,
        args.method,
        args.columns,
        args.skip_columns,
        args.drop_columns,
        args.decimal_places
    )