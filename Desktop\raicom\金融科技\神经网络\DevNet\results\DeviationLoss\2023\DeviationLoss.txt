PS G:\Curriculum_Design\DevNet> python .\train.py
Index(['V1', 'V2', 'V3', 'V4', 'V5', 'V6', 'V7', 'V8', 'V9', 'V10', 'V11',
       'V12', 'V13', 'V14', 'V15', 'V16', 'V17', 'V18', 'V19', 'V20', 'V21',
       'V22', 'V23', 'V24', 'V25', 'V26', 'V27', 'V28', 'Amount', 'Class'],
      dtype='object')
Data shape: (568630, 29)
creditcard_2023_normalize: round 0
Original training size: 454904, No. outliers: 227452
232123 30 232093 4641
Training data size: 232123, No. outliers: 30
Epoch 0, Loss: 2.097024005651474
Epoch 10, Loss: 0.31216696873307226
Epoch 20, Loss: 0.32736698910593987
Epoch 30, Loss: 0.31603576615452766
Epoch 40, Loss: 0.3024128168821335
AUC-ROC: 0.9414, AUC-PR: 0.9615

=== 整个测试集指标 ===
准确率: 0.8956
精度: 0.8956
F1分数: 0.8956

=== 正常数据指标 ===
样本数: 56863
准确率: 0.8956
精度: 1.0000
F1分数: 0.9449

=== 异常数据指标 ===
样本数: 56863
准确率: 0.8956
精度: 1.0000
F1分数: 0.9449
==================================================
average AUC-ROC: 0.9414, average AUC-PR: 0.9615
average runtime: 55.1378 seconds
PS G:\Curriculum_Design\DevNet> 