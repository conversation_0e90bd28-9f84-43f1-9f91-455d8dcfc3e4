import torch
import torch.optim as optim
import torch.nn as nn
from torch.utils.data import DataLoader
import argparse
import os
import time
import numpy as np
from sklearn.metrics import accuracy_score, precision_score, f1_score

from improved_loss import *
from model import create_network, DeviationLoss, get_device
from utils import aucPerformance, writeResults, DevNetDataset

# 设置随机种子
np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed(42)

def train_from_generated_data(args):
    names = args.data_set.split(',')
    network_depth = int(args.network_depth)
    device = get_device()

    for nm in names:
        runs = args.runs
        rauc = np.zeros(runs)
        ap = np.zeros(runs)
        filename = nm.strip()

        # 创建模型保存目录
        model_dir = os.path.join('./model', filename)
        os.makedirs(model_dir, exist_ok=True)

        # 数据目录
        train_data_dir = os.path.join('./dataset', filename, 'train')
        test_data_dir = os.path.join('./dataset', filename, 'test')

        train_time = 0
        test_time = 0

        for i in range(runs):
            print(f'{filename}: 第 {i+1} 轮训练')

            # 加载训练数据
            train_filename = f"train_data_run{i+1}.npz"
            train_path = os.path.join(train_data_dir, train_filename)

            if not os.path.exists(train_path):
                print(f"训练数据文件不存在: {train_path}")
                print("请先运行 generate_data.py 生成训练数据")
                continue

            train_data = np.load(train_path)
            x_train = train_data['x_train']
            y_train = train_data['y_train']
            fraud_indices = train_data['fraud_indices']
            normal_indices = train_data['normal_indices']

            print(f"加载训练数据: {len(x_train)} 样本")

            # 加载测试数据
            test_filename = f"test_data_run{i+1}.npz"
            test_path = os.path.join(test_data_dir, test_filename)

            if not os.path.exists(test_path):
                print(f"测试数据文件不存在: {test_path}")
                continue

            test_data = np.load(test_path)
            x_test = test_data['x_test']
            y_test = test_data['y_test']

            print(f"加载测试数据: {len(x_test)} 样本")

            input_dim = x_train.shape[1]

            # 创建数据集和数据加载器
            train_dataset = DevNetDataset(x_train, fraud_indices, normal_indices)
            train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)

            model = create_network(input_dim, network_depth).to(device)
            criterion = AUCPRLoss().to(device)
            optimizer = optim.RMSprop(model.parameters(), lr=0.001,
                                    weight_decay=model.weight_decay if hasattr(model, 'weight_decay') else 0)

            start_time = time.time()
            model.train()

            for epoch in range(args.epochs):
                running_loss = 0.0
                for batch_idx, (data, target) in enumerate(train_loader):
                    if batch_idx >= args.nb_batch:
                        break

                    data, target = data.to(device), target.to(device)

                    optimizer.zero_grad()
                    outputs = model(data)
                    # 确保target的维度正确
                    target = target.squeeze()  # 移除多余的维度
                    loss = criterion(outputs.squeeze(), target)
                    loss.backward()
                    optimizer.step()

                    running_loss += loss.item()

                if epoch % 10 == 0:
                    print(f'Epoch {epoch}, Loss: {running_loss / args.nb_batch}')

            train_time += time.time() - start_time

            # 保存训练好的模型
            loss_name = criterion.__class__.__name__
            model_filename = f"{loss_name}_{args.batch_size}bs_{args.epochs}ep_{network_depth}d_run{i+1}.pt"
            model_path = os.path.join(model_dir, model_filename)
            torch.save(model.state_dict(), model_path)
            print(f"模型已保存到: {model_path}")

            # 测试模型
            start_time = time.time()
            model.eval()

            with torch.no_grad():
                x_test_tensor = torch.FloatTensor(x_test).to(device)
                scores = model(x_test_tensor).squeeze().cpu().numpy()  # 确保是一维数组

            test_time += time.time() - start_time
            rauc[i], ap[i] = aucPerformance(scores, y_test)

            # 计算分类指标
            threshold = np.median(scores)
            y_pred = (scores > threshold).astype(int)

            # 1. 整个测试集的指标
            overall_accuracy = accuracy_score(y_test, y_pred)
            overall_precision = precision_score(y_test, y_pred, zero_division=0)
            overall_f1 = f1_score(y_test, y_pred, zero_division=0)

            print(f"\n=== 整个测试集指标 ===")
            print(f"准确率: {overall_accuracy:.4f}")
            print(f"精度: {overall_precision:.4f}")
            print(f"F1分数: {overall_f1:.4f}")

            # 2. 只有正常数据的指标
            normal_test_indices = np.where(y_test == 0)[0]
            if len(normal_test_indices) > 0:
                normal_pred = y_pred[normal_test_indices]
                normal_true = y_test[normal_test_indices]

                normal_accuracy = accuracy_score(normal_true, normal_pred)
                normal_precision = precision_score(1-normal_true, 1-normal_pred, zero_division=0)
                normal_f1 = f1_score(1-normal_true, 1-normal_pred, zero_division=0)

                print(f"\n=== 正常数据指标 ===")
                print(f"样本数: {len(normal_test_indices)}")
                print(f"准确率: {normal_accuracy:.4f}")
                print(f"精度: {normal_precision:.4f}")
                print(f"F1分数: {normal_f1:.4f}")

            # 3. 只有异常数据的指标
            fraud_test_indices = np.where(y_test == 1)[0]
            if len(fraud_test_indices) > 0:
                fraud_pred = y_pred[fraud_test_indices]
                fraud_true = y_test[fraud_test_indices]

                fraud_accuracy = accuracy_score(fraud_true, fraud_pred)
                fraud_precision = precision_score(fraud_true, fraud_pred, zero_division=0)
                fraud_f1 = f1_score(fraud_true, fraud_pred, zero_division=0)

                print(f"\n=== 异常数据指标 ===")
                print(f"样本数: {len(fraud_test_indices)}")
                print(f"准确率: {fraud_accuracy:.4f}")
                print(f"精度: {fraud_precision:.4f}")
                print(f"F1分数: {fraud_f1:.4f}")

            print("=" * 50)

        mean_auc = np.mean(rauc)
        std_auc = np.std(rauc)
        mean_aucpr = np.mean(ap)
        std_aucpr = np.std(ap)
        train_time = train_time/runs
        test_time = test_time/runs

        print(f"\n=== 最终结果 ===")
        print(f"平均 AUC-ROC: {mean_auc:.4f} ± {std_auc:.4f}")
        print(f"平均 AUC-PR: {mean_aucpr:.4f} ± {std_aucpr:.4f}")
        print(f"平均运行时间: {train_time + test_time:.4f} 秒")

        # 保存结果
        writeResults(filename+'_from_generated_'+str(network_depth),
                    0, x_train.shape[1], len(x_train), 0, len(fraud_test_indices),
                    network_depth, mean_auc, mean_aucpr, std_auc, std_aucpr,
                    train_time, test_time, path=args.output)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--network_depth", choices=['1','2', '4'], default='4', help="网络深度")
    parser.add_argument("--batch_size", type=int, default=512, help="批次大小")
    parser.add_argument("--nb_batch", type=int, default=20, help="每个epoch的批次数")
    parser.add_argument("--epochs", type=int, default=50, help="训练轮数")
    parser.add_argument("--runs", type=int, default=1, help="实验重复次数")
    parser.add_argument("--data_set", type=str, default='creditcardfraud_normalised_2013', help="数据集名称")
    parser.add_argument("--output", type=str, default='./results/devnet_from_generated_results.csv', help="输出文件路径")
    args = parser.parse_args()
    train_from_generated_data(args)