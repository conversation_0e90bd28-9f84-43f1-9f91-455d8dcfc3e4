import torch
import torch.optim as optim
import torch.nn as nn
from torch.utils.data import DataLoader
import argparse
import os
import time
import numpy as np
from sklearn.metrics import accuracy_score, precision_score, f1_score
from sklearn.model_selection import train_test_split

from model import create_network, DeviationLoss, get_device
from utils import aucPerformance, writeResults, DevNetDataset

# 设置随机种子
np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed(42)

def ensemble_train_5models(args):
    names = args.data_set.split(',')
    network_depth = int(args.network_depth)
    device = get_device()
    
    for nm in names:
        filename = nm.strip()
        print(f"开始集成训练: {filename}")
        
        # 创建模型保存目录
        model_dir = os.path.join('./model', filename, 'ensemble_5models')
        os.makedirs(model_dir, exist_ok=True)
        
        # 数据目录
        train_data_dir = os.path.join('./dataset', filename, 'train')
        test_data_dir = os.path.join('./dataset', filename, 'test')
        
        # 加载训练数据
        train_filename = f"train_data_run1.npz"
        train_path = os.path.join(train_data_dir, train_filename)
        
        if not os.path.exists(train_path):
            print(f"训练数据文件不存在: {train_path}")
            print("请先运行 generate_data.py 生成训练数据")
            continue
            
        train_data = np.load(train_path)
        x_train_full = train_data['x_train']
        y_train_full = train_data['y_train']
        fraud_indices_full = train_data['fraud_indices']
        normal_indices_full = train_data['normal_indices']
        
        print(f"加载完整训练数据: {len(x_train_full)} 样本")
        print(f"诈骗样本: {len(fraud_indices_full)}, 正常样本: {len(normal_indices_full)}")
        
        # 加载测试数据
        test_filename = f"test_data_run1.npz"
        test_path = os.path.join(test_data_dir, test_filename)
        
        if not os.path.exists(test_path):
            print(f"测试数据文件不存在: {test_path}")
            continue
            
        test_data = np.load(test_path)
        x_test = test_data['x_test']
        y_test = test_data['y_test']
        
        print(f"加载测试数据: {len(x_test)} 样本")
        print(f"测试集诈骗样本: {np.sum(y_test)}, 正常样本: {len(y_test) - np.sum(y_test)}")
        
        input_dim = x_train_full.shape[1]
        
        # 将训练数据分成5份
        print("\n将训练数据分成5份...")
        
        # 分别对诈骗样本和正常样本进行分割
        fraud_data = x_train_full[fraud_indices_full]
        normal_data = x_train_full[normal_indices_full]
        
        # 将诈骗样本分成5份
        fraud_splits = np.array_split(fraud_data, 5)
        fraud_idx_splits = np.array_split(fraud_indices_full, 5)
        
        # 将正常样本分成5份
        normal_splits = np.array_split(normal_data, 5)
        normal_idx_splits = np.array_split(normal_indices_full, 5)
        
        # 存储5个模型和每个模型的预测结果
        models = []
        model_paths = []
        individual_scores = []
        individual_predictions = []
        individual_aucs = []
        
        total_train_time = 0
        total_test_time = 0
        
        # 训练5个模型
        for model_idx in range(5):
            print(f"\n=== 训练模型 {model_idx + 1}/5 ===")
            
            # 组合当前模型的训练数据
            current_fraud_data = fraud_splits[model_idx]
            current_normal_data = normal_splits[model_idx]
            current_fraud_indices = fraud_idx_splits[model_idx]
            current_normal_indices = normal_idx_splits[model_idx]
            
            # 重新构建训练数据
            x_train_current = np.vstack([current_fraud_data, current_normal_data])
            
            # 重新构建索引（相对于当前训练集）
            fraud_indices_current = np.arange(len(current_fraud_data))
            normal_indices_current = np.arange(len(current_fraud_data), len(x_train_current))
            
            print(f"模型 {model_idx + 1} 训练数据: {len(x_train_current)} 样本")
            print(f"诈骗样本: {len(fraud_indices_current)}, 正常样本: {len(normal_indices_current)}")
            
            # 创建数据集和数据加载器
            train_dataset = DevNetDataset(x_train_current, fraud_indices_current, normal_indices_current)
            train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True)
            
            # 创建模型
            model = create_network(input_dim, network_depth).to(device)
            criterion = DeviationLoss().to(device)
            optimizer = optim.RMSprop(model.parameters(), lr=0.001, 
                                    weight_decay=model.weight_decay if hasattr(model, 'weight_decay') else 0)
            
            # 训练模型
            start_time = time.time()
            model.train()
            
            for epoch in range(args.epochs):
                running_loss = 0.0
                batch_count = 0
                
                for batch_idx, (data, target) in enumerate(train_loader):
                    if batch_idx >= args.nb_batch:
                        break
                        
                    data, target = data.to(device), target.to(device)
                    
                    optimizer.zero_grad()
                    outputs = model(data)
                    loss = criterion(outputs, target)
                    loss.backward()
                    optimizer.step()
                    
                    running_loss += loss.item()
                    batch_count += 1
                    
                if epoch % 10 == 0 and batch_count > 0:
                    print(f'模型 {model_idx + 1}, Epoch {epoch}, Loss: {running_loss / batch_count:.4f}')
            
            train_time = time.time() - start_time
            total_train_time += train_time
            
            # 保存模型
            loss_name = criterion.__class__.__name__
            model_filename = f"{loss_name}_{args.batch_size}bs_{args.epochs}ep_{network_depth}d_model{model_idx + 1}.pt"
            model_path = os.path.join(model_dir, model_filename)
            torch.save(model.state_dict(), model_path)
            print(f"模型 {model_idx + 1} 已保存到: {model_path}")
            
            # 立即测试当前模型
            print(f"\n=== 测试模型 {model_idx + 1} ===")
            start_test_time = time.time()
            model.eval()
            
            with torch.no_grad():
                x_test_tensor = torch.FloatTensor(x_test).to(device)
                scores = model(x_test_tensor).cpu().numpy()
            
            test_time = time.time() - start_test_time
            total_test_time += test_time
            
            # 计算AUC指标
            auc_roc, auc_pr = aucPerformance(scores, y_test)
            individual_aucs.append((auc_roc, auc_pr))
            
            # 使用中位数作为阈值进行二分类
            threshold = np.median(scores)
            predictions = (scores > threshold).astype(int)
            
            # 计算分类指标
            overall_accuracy = accuracy_score(y_test, predictions)
            overall_precision = precision_score(y_test, predictions, zero_division=0)
            overall_f1 = f1_score(y_test, predictions, zero_division=0)
            
            print(f"AUC-ROC: {auc_roc:.4f}, AUC-PR: {auc_pr:.4f}")
            print(f"阈值: {threshold:.4f}")
            print(f"整个测试集 - 准确率: {overall_accuracy:.4f}, 精度: {overall_precision:.4f}, F1: {overall_f1:.4f}")
            
            # 正常数据指标
            normal_test_indices = np.where(y_test == 0)[0]
            if len(normal_test_indices) > 0:
                normal_pred = predictions[normal_test_indices]
                normal_true = y_test[normal_test_indices]
                
                normal_accuracy = accuracy_score(normal_true, normal_pred)
                normal_precision = precision_score(1-normal_true, 1-normal_pred, zero_division=0)
                normal_f1 = f1_score(1-normal_true, 1-normal_pred, zero_division=0)
                
                print(f"正常数据 ({len(normal_test_indices)}样本) - 准确率: {normal_accuracy:.4f}, 精度: {normal_precision:.4f}, F1: {normal_f1:.4f}")
            
            # 异常数据指标
            fraud_test_indices = np.where(y_test == 1)[0]
            if len(fraud_test_indices) > 0:
                fraud_pred = predictions[fraud_test_indices]
                fraud_true = y_test[fraud_test_indices]
                
                fraud_accuracy = accuracy_score(fraud_true, fraud_pred)
                fraud_precision = precision_score(fraud_true, fraud_pred, zero_division=0)
                fraud_f1 = f1_score(fraud_true, fraud_pred, zero_division=0)
                
                print(f"异常数据 ({len(fraud_test_indices)}样本) - 准确率: {fraud_accuracy:.4f}, 精度: {fraud_precision:.4f}, F1: {fraud_f1:.4f}")
            
            # 存储结果
            models.append(model)
            model_paths.append(model_path)
            individual_scores.append(scores)
            individual_predictions.append(predictions)
        
        print(f"\n=== 5个模型训练完成 ===")
        print(f"总训练时间: {total_train_time:.2f} 秒")
        print(f"总测试时间: {total_test_time:.2f} 秒")
        
        # 集成预测
        print("\n=== 集成预测结果 ===")
        
        # 转换为numpy数组
        all_scores = np.array(individual_scores)  # shape: (5, num_samples)
        all_predictions = np.array(individual_predictions)  # shape: (5, num_samples)
        
        # 计算平均分数（用于AUC计算）
        avg_scores = np.mean(all_scores, axis=0)
        
        # 集成预测：如果有5个以上模型预测为诈骗（1），则预测为诈骗（1）
        # 否则预测为正常（0）
        vote_counts_for_fraud = np.sum(all_predictions == 1, axis=0)  # 预测为诈骗的模型数量
        ensemble_predictions = (vote_counts_for_fraud >= 5).astype(int)  # 5个以上预测为诈骗则为诈骗（1）
        
        print(f"使用投票规则: 5个以上模型预测为诈骗则为诈骗")
        print(f"预测为诈骗的数量: {np.sum(ensemble_predictions)}")
        print(f"预测为正常的数量: {np.sum(1 - ensemble_predictions)}")
        
        # 计算集成AUC指标（使用平均分数）
        ensemble_auc_roc, ensemble_auc_pr = aucPerformance(avg_scores, y_test)
        
        # 计算集成分类指标
        # 1. 整个测试集的指标
        overall_accuracy = accuracy_score(y_test, ensemble_predictions)
        overall_precision = precision_score(y_test, ensemble_predictions, zero_division=0)
        overall_f1 = f1_score(y_test, ensemble_predictions, zero_division=0)
        
        print(f"\n=== 集成模型 - 整个测试集指标 ===")
        print(f"AUC-ROC: {ensemble_auc_roc:.4f}")
        print(f"AUC-PR: {ensemble_auc_pr:.4f}")
        print(f"准确率: {overall_accuracy:.4f}")
        print(f"精度: {overall_precision:.4f}")
        print(f"F1分数: {overall_f1:.4f}")
        
        # 2. 只有正常数据的指标
        normal_test_indices = np.where(y_test == 0)[0]
        if len(normal_test_indices) > 0:
            normal_pred = ensemble_predictions[normal_test_indices]
            normal_true = y_test[normal_test_indices]
            
            normal_accuracy = accuracy_score(normal_true, normal_pred)
            normal_precision = precision_score(1-normal_true, 1-normal_pred, zero_division=0)
            normal_f1 = f1_score(1-normal_true, 1-normal_pred, zero_division=0)
            
            print(f"\n=== 集成模型 - 正常数据指标 ===")
            print(f"样本数: {len(normal_test_indices)}")
            print(f"准确率: {normal_accuracy:.4f}")
            print(f"精度: {normal_precision:.4f}")
            print(f"F1分数: {normal_f1:.4f}")
        
        # 3. 只有异常数据的指标
        fraud_test_indices = np.where(y_test == 1)[0]
        if len(fraud_test_indices) > 0:
            fraud_pred = ensemble_predictions[fraud_test_indices]
            fraud_true = y_test[fraud_test_indices]
            
            fraud_accuracy = accuracy_score(fraud_true, fraud_pred)
            fraud_precision = precision_score(fraud_true, fraud_pred, zero_division=0)
            fraud_f1 = f1_score(fraud_true, fraud_pred, zero_division=0)
            
            print(f"\n=== 集成模型 - 异常数据指标 ===")
            print(f"样本数: {len(fraud_test_indices)}")
            print(f"准确率: {fraud_accuracy:.4f}")
            print(f"精度: {fraud_precision:.4f}")
            print(f"F1分数: {fraud_f1:.4f}")
        
        print("=" * 50)
        
        # 显示每个模型的AUC结果
        print(f"\n=== 各个模型AUC结果 ===")
        for i, (auc_roc, auc_pr) in enumerate(individual_aucs):
            print(f"模型 {i+1}: AUC-ROC={auc_roc:.4f}, AUC-PR={auc_pr:.4f}")
        
        print(f"\n=== 最终集成结果 ===")
        print(f"集成 AUC-ROC: {ensemble_auc_roc:.4f}")
        print(f"集成 AUC-PR: {ensemble_auc_pr:.4f}")
        print(f"总训练时间: {total_train_time:.2f} 秒")
        print(f"总测试时间: {total_test_time:.2f} 秒")
        
        # 保存集成预测结果
        results_dir = os.path.join('./results')
        os.makedirs(results_dir, exist_ok=True)
        
        results_filename = f"ensemble_5models_{filename}_vote4.npz"
        results_path = os.path.join(results_dir, results_filename)
        
        np.savez(results_path,
                ensemble_predictions=ensemble_predictions,
                avg_scores=avg_scores,
                y_test=y_test,
                all_predictions=all_predictions,
                all_scores=all_scores,
                individual_aucs=individual_aucs,
                ensemble_auc_roc=ensemble_auc_roc,
                ensemble_auc_pr=ensemble_auc_pr,
                overall_accuracy=overall_accuracy,
                overall_precision=overall_precision,
                overall_f1=overall_f1,
                model_paths=model_paths)
        
        print(f"\n集成预测结果已保存到: {results_path}")
        
        # 显示投票统计
        print(f"\n=== 投票统计 ===")
        vote_counts_fraud = np.sum(all_predictions == 1, axis=0)  # 预测为诈骗的模型数量
        for i in range(6):  # 0到5票
            count = np.sum(vote_counts_fraud == i)
            print(f"预测为诈骗的模型数 = {i}: {count} 样本")
        
        # 保存结果到CSV（与原训练脚本格式一致）
        writeResults(filename+'_ensemble_5models_'+str(network_depth), 
                    0, x_train_full.shape[1], len(x_train_full), 0, len(fraud_test_indices),
                    network_depth, ensemble_auc_roc, ensemble_auc_pr, 0, 0, 
                    total_train_time, total_test_time, path=args.output)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--network_depth", choices=['1','2', '4'], default='4', help="网络深度")
    parser.add_argument("--batch_size", type=int, default=512, help="批次大小")
    parser.add_argument("--nb_batch", type=int, default=20, help="每个epoch的批次数")
    parser.add_argument("--epochs", type=int, default=50, help="训练轮数")
    parser.add_argument("--data_set", type=str, default='creditcardfraud_normalised', help="数据集名称")
    parser.add_argument("--output", type=str, default='./results/ensemble_5models_results.csv', help="输出文件路径")
    args = parser.parse_args()
    ensemble_train_5models(args)