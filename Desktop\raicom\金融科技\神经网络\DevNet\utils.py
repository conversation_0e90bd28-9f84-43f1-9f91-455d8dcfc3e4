import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from sklearn.metrics import roc_auc_score, average_precision_score
from scipy.sparse import csc_matrix
import os
import joblib
from sklearn.datasets import load_svmlight_file

# 创建内存缓存目录用于存储预处理的数据，加速后续加载
os.makedirs("./dataset/svm_data", exist_ok=True)
mem = joblib.Memory("./dataset/svm_data")

@mem.cache
def get_data_from_svmlight_file(path):
    """
    从svmlight格式文件加载数据并进行缓存
    
    参数:
    path: 文件路径
    
    返回:
    特征矩阵和标签
    """
    data = load_svmlight_file(path)
    return data[0], data[1]

def dataLoading(path):
    """
    从CSV文件加载数据
    
    参数:
    path: CSV文件路径
    
    返回:
    特征矩阵和标签
    """
    # 加载数据
    df = pd.read_csv(path) 
    print(df.columns)
    
    # 提取标签列
    labels = df['class']
    
    # 移除标签列获取特征
    x_df = df.drop(['class'], axis=1)
    
    # 转换为numpy数组
    x = x_df.values
    print("Data shape: (%d, %d)" % x.shape)
    
    return x, labels

def aucPerformance(scores, labels):
    """
    计算并打印AUC-ROC和AUC-PR性能指标
    
    参数:
    scores: 模型预测分数
    labels: 真实标签
    
    返回:
    AUC-ROC和AUC-PR值
    """
    roc_auc = roc_auc_score(labels, scores)
    ap = average_precision_score(labels, scores)
    print("AUC-ROC: %.4f, AUC-PR: %.4f" % (roc_auc, ap))
    return roc_auc, ap

def writeResults(name, n_samples, dim, n_samples_trn, n_outliers_trn, n_outliers, depth, rauc, ap, std_auc, std_ap, train_time, test_time, path="./results/auc_performance_cl0.5.csv"):
    """
    将实验结果写入CSV文件
    
    参数:
    name: 数据集名称
    n_samples: 总样本数
    dim: 特征维度
    n_samples_trn: 训练样本数
    n_outliers_trn: 训练集中的异常样本数
    n_outliers: 测试集中的异常样本数
    depth: 模型深度
    rauc: AUC-ROC均值
    ap: AUC-PR均值
    std_auc: AUC-ROC标准差
    std_ap: AUC-PR标准差
    train_time: 训练时间
    test_time: 测试时间
    path: 结果文件路径
    """
    os.makedirs(os.path.dirname(path), exist_ok=True)
    csv_file = open(path, 'a') 
    row = name + "," + str(n_samples)+ ","  + str(dim) + ',' + str(n_samples_trn) + ','+ str(n_outliers_trn) + ','+ str(n_outliers)  + ',' + str(depth)+ "," + str(rauc) +"," + str(std_auc) + "," + str(ap) +"," + str(std_ap)+"," + str(train_time)+"," + str(test_time) + "\n"
    csv_file.write(row)

class DevNetDataset(Dataset):
    """
    DevNet模型的自定义数据集类，用于生成训练样本
    
    每个epoch会生成交替的正常和异常样本
    """
    def __init__(self, data, outlier_indices, inlier_indices):
        """
        初始化数据集
        
        参数:
        data: 特征数据
        outlier_indices: 异常样本索引
        inlier_indices: 正常样本索引
        """
        self.data = data
        self.outlier_indices = outlier_indices
        self.inlier_indices = inlier_indices
        self.n_inliers = len(inlier_indices)
        self.n_outliers = len(outlier_indices)
        
    def __len__(self):
        """返回每个epoch中的样本数量"""
        return 512 * 20  # batch_size * nb_batch
    
    def __getitem__(self, idx):
        """
        获取单个样本
        
        参数:
        idx: 样本索引
        
        返回:
        特征张量和标签张量
        """
        # 交替生成正常样本和异常样本
        if idx % 2 == 0:
            # 正常样本
            idx = np.random.choice(self.n_inliers, 1)[0]
            sample = self.data[self.inlier_indices[idx]]
            label = 0
        else:
            # 异常样本
            idx = np.random.choice(self.n_outliers, 1)[0]
            sample = self.data[self.outlier_indices[idx]]
            label = 1
            
        return torch.FloatTensor(sample), torch.FloatTensor([label])

def inject_noise_sparse(seed, n_out, random_seed):
    '''
    向训练数据添加异常以复制异常污染的数据集。
    我们随机交换异常的5%特征以避免重复的污染异常。
    这适用于稀疏数据。
    
    参数:
    seed: 异常种子数据
    n_out: 需要生成的异常样本数
    random_seed: 随机种子
    
    返回:
    生成的噪声(异常)数据
    '''
    rng = np.random.RandomState(random_seed) 
    n_sample, dim = seed.shape
    swap_ratio = 0.05
    n_swap_feat = int(swap_ratio * dim)
    seed = seed.tocsc()
    noise = csc_matrix((n_out, dim))
    print(noise.shape)
    for i in np.arange(n_out):
        outlier_idx = rng.choice(n_sample, 2, replace=False)
        o1 = seed[outlier_idx[0]]
        o2 = seed[outlier_idx[1]]
        swap_feats = rng.choice(dim, n_swap_feat, replace=False)
        noise[i] = o1.copy()
        noise[i, swap_feats] = o2[0, swap_feats]
    return noise.tocsr()

def inject_noise(seed, n_out, random_seed):
    '''
    向训练数据添加异常以复制异常污染的数据集。
    我们随机交换异常的5%特征以避免重复的污染异常。
    这适用于密集数据。
    
    参数:
    seed: 异常种子数据
    n_out: 需要生成的异常样本数
    random_seed: 随机种子
    
    返回:
    生成的噪声(异常)数据
    '''
    rng = np.random.RandomState(random_seed) 
    n_sample, dim = seed.shape
    swap_ratio = 0.05
    n_swap_feat = int(swap_ratio * dim)
    noise = np.empty((n_out, dim))
    for i in np.arange(n_out):
        outlier_idx = rng.choice(n_sample, 2, replace=False)
        o1 = seed[outlier_idx[0]]
        o2 = seed[outlier_idx[1]]
        swap_feats = rng.choice(dim, n_swap_feat, replace=False)
        noise[i] = o1.copy()
        noise[i, swap_feats] = o2[swap_feats]
    return noise