import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np


class DevNetD(nn.Module):
    '''
    深层网络架构，有三个隐藏层
    '''
    def __init__(self, input_dim):
        super(DevNetD, self).__init__()
        self.layer1 = nn.Linear(input_dim, 1000)
        self.layer2 = nn.Linear(1000, 250)
        self.layer3 = nn.Linear(250, 20)
        self.layer4 = nn.Linear(20, 1)
        
        self.weight_decay = 0.01
        
    def forward(self, x):
        x = F.relu(self.layer1(x))
        x = F.relu(self.layer2(x))
        x = F.relu(self.layer3(x))
        return self.layer4(x)

class DevNetS(nn.Module):
    '''
    单隐藏层网络架构
    '''
    def __init__(self, input_dim):
        super(DevNetS, self).__init__()
        self.layer1 = nn.Linear(input_dim, 20)
        self.layer2 = nn.Linear(20, 1)
        
        self.weight_decay = 0.01
        
    def forward(self, x):
        x = F.relu(self.layer1(x))
        return self.layer2(x)

class DevNetLinear(nn.Module):
    '''
    无隐藏层的网络架构，相当于从原始输入到异常分数的线性映射
    '''
    def __init__(self, input_dim):
        super(DevNetLinear, self).__init__()
        self.layer = nn.Linear(input_dim, 1)
        
    def forward(self, x):
        return self.layer(x)

class DeviationLoss(nn.Module):
    '''
    基于z分数的偏差损失
    '''
    def __init__(self, ref_size=5000):
        super(DeviationLoss, self).__init__()
        self.confidence_margin = 5.0
        # 创建参考分布
        self.register_buffer('ref_dist', torch.FloatTensor(np.random.normal(loc=0., scale=1.0, size=ref_size)))

        
    def forward(self, y_pred, y_true):
        # 计算z分数偏差
        dev = (y_pred - torch.mean(self.ref_dist)) / torch.std(self.ref_dist)
        
        # 内部样本损失和异常样本损失
        inlier_loss = torch.abs(dev)
        outlier_loss = torch.abs(torch.clamp(self.confidence_margin - dev, min=0.))
        
        # 总损失
        loss = torch.mean((1 - y_true) * inlier_loss + y_true * outlier_loss)
        return loss

def get_device():
    return torch.device("cuda:0" if torch.cuda.is_available() else "cpu")

def create_network(input_dim, network_depth):

    if network_depth == 4:
        model = DevNetD(input_dim)
    elif network_depth == 2:
        model = DevNetS(input_dim)
    elif network_depth == 1:
        model = DevNetLinear(input_dim)
    else:
        raise ValueError("网络深度设置不正确")
    
    return model