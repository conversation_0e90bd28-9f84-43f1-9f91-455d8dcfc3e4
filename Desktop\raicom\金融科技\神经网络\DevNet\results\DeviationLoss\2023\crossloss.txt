PS G:\Curriculum_Design\DevNet> python .\train.py
Index(['V1', 'V2', 'V3', 'V4', 'V5', 'V6', 'V7', 'V8', 'V9', 'V10', 'V11',
       'V12', 'V13', 'V14', 'V15', 'V16', 'V17', 'V18', 'V19', 'V20', 'V21',
       'V22', 'V23', 'V24', 'V25', 'V26', 'V27', 'V28', 'Amount', 'Class'],
      dtype='object')
Data shape: (568630, 29)
creditcard_2023_normalize: round 0
Original training size: 454904, No. outliers: 227452
232123 30 232093 4641
Training data size: 232123, No. outliers: 30
Epoch 0, Loss: 0.0
Epoch 10, Loss: 0.0
Epoch 20, Loss: 0.0
Epoch 30, Loss: 0.0
Epoch 40, Loss: 0.0
AUC-ROC: 0.4590, AUC-PR: 0.5011

=== 整个测试集指标 ===
准确率: 0.4725
精度: 0.4725
F1分数: 0.4724

=== 正常数据指标 ===
样本数: 56863
准确率: 0.4725
精度: 1.0000
F1分数: 0.6418

=== 异常数据指标 ===
样本数: 56863
准确率: 0.4724
精度: 1.0000
F1分数: 0.6417
==================================================
average AUC-ROC: 0.4590, average AUC-PR: 0.5011
average runtime: 55.6269 seconds
PS G:\Curriculum_Design\DevNet> 